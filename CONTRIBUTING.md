# Contributing to NESTOR

Thank you for your interest in contributing to NESTOR! This document provides guidelines and information for contributors.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [Contributing Process](#contributing-process)
5. [Coding Standards](#coding-standards)
6. [Testing Guidelines](#testing-guidelines)
7. [Documentation](#documentation)
8. [Pull Request Process](#pull-request-process)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- Be respectful and inclusive
- Focus on constructive feedback
- Help create a welcoming environment for all contributors
- Respect different viewpoints and experiences

## Getting Started

### Prerequisites

- Python 3.11+
- Podman or Docker
- Git
- Basic understanding of microservices architecture
- Familiarity with FastAPI and PostgreSQL

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nestor
   ```

2. **Initialize the project**
   ```bash
   make init
   ```

3. **Start development environment**
   ```bash
   make up
   ```

4. **Verify setup**
   ```bash
   make health-check
   ```

## Contributing Process

1. **Fork the repository** on GitHub
2. **Create a feature branch** from `main`
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes** following our coding standards
4. **Write tests** for new functionality
5. **Update documentation** as needed
6. **Commit your changes** with clear messages
7. **Push to your fork** and create a Pull Request

## Coding Standards

### Python Code Style

- Follow [PEP 8](https://pep8.org/) style guide
- Use [Black](https://black.readthedocs.io/) for code formatting
- Use [isort](https://pycqa.github.io/isort/) for import sorting
- Use type hints for all function parameters and return values
- Maximum line length: 88 characters (Black default)

### Code Quality Tools

```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

### Naming Conventions

- **Variables and functions**: `snake_case`
- **Classes**: `PascalCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Files and modules**: `snake_case`
- **API endpoints**: `kebab-case` in URLs

### Documentation Standards

- Use docstrings for all public functions, classes, and modules
- Follow [Google docstring format](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)
- Include type information in docstrings
- Provide examples for complex functions

Example:
```python
def create_personality(
    name: str, 
    description: str, 
    user_id: UUID
) -> Personality:
    """Create a new digital personality.
    
    Args:
        name: The name of the personality
        description: A description of the personality
        user_id: The ID of the user creating the personality
        
    Returns:
        The created Personality object
        
    Raises:
        ValidationError: If the input data is invalid
        DatabaseError: If the database operation fails
        
    Example:
        >>> personality = create_personality(
        ...     name="Albert Einstein",
        ...     description="Theoretical physicist",
        ...     user_id=user.id
        ... )
    """
```

## Testing Guidelines

### Test Structure

- Place tests in the `tests/` directory
- Mirror the source code structure in test directories
- Use descriptive test names that explain what is being tested

### Test Types

1. **Unit Tests**: Test individual functions and classes
2. **Integration Tests**: Test service interactions
3. **End-to-End Tests**: Test complete user workflows

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_api/test_personalities.py

# Run tests with verbose output
pytest -v
```

### Test Naming

```python
def test_create_personality_with_valid_data_should_return_personality():
    """Test that creating a personality with valid data returns a Personality object."""
    pass

def test_create_personality_with_invalid_name_should_raise_validation_error():
    """Test that creating a personality with invalid name raises ValidationError."""
    pass
```

## Documentation

### Types of Documentation

1. **Code Documentation**: Docstrings and inline comments
2. **API Documentation**: OpenAPI/Swagger specifications
3. **User Documentation**: README, guides, tutorials
4. **Developer Documentation**: Architecture, setup, contributing

### Documentation Updates

- Update documentation when adding new features
- Include examples and use cases
- Keep documentation in sync with code changes
- Use clear, concise language

## Pull Request Process

### Before Submitting

1. **Ensure all tests pass**
   ```bash
   pytest
   ```

2. **Check code quality**
   ```bash
   black --check .
   isort --check-only .
   flake8 .
   mypy .
   ```

3. **Update documentation** if needed

4. **Add changelog entry** if applicable

### Pull Request Template

When creating a PR, include:

- **Description**: What does this PR do?
- **Type**: Feature, bugfix, documentation, etc.
- **Testing**: How was this tested?
- **Breaking Changes**: Any breaking changes?
- **Related Issues**: Link to related issues

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** in development environment
4. **Documentation review** if applicable
5. **Approval** and merge

## Development Workflow

### Branch Naming

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Messages

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

Examples:
```
feat(api): add personality creation endpoint

fix(memory): resolve memory leak in vector storage

docs(readme): update installation instructions
```

## Getting Help

- **Issues**: Create GitHub issues for bugs and feature requests
- **Discussions**: Use GitHub Discussions for questions
- **Documentation**: Check the README and docs/ directory
- **Code Review**: Ask for help in pull request comments

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to NESTOR!
