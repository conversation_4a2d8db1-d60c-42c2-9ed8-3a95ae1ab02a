"""
Minimální test aplikace pro Memory Context Processor
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="NESTOR Memory Test")

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "memory-test"}

@app.get("/")
async def root():
    return {"message": "NESTOR Memory Context Processor Test"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
