"""
Context Processor - zpracování kontextu s podporou persona_core

Tento modul rozšiřuje zpracování kontextu o ukládání stylov<PERSON> v<PERSON>ev,
emo<PERSON><PERSON><PERSON><PERSON> stavů a behaviorálních vzorců z persona_core modulu.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
import structlog
import json

from .memory_types import Memory, MemoryType, MemoryQuery
from persona_core.affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait
from persona_core.trace_replayer import BehavioralTrace, TraceType

logger = structlog.get_logger()


class ContextState(BaseModel):
    """Stav kontextu pro digitální osobnost"""
    
    # Identifikace
    persona_id: str
    session_id: str
    
    # Aktuální kontext
    current_conversation: List[Dict[str, str]] = Field(default_factory=list)
    active_memories: List[str] = Field(default_factory=list)  # Memory IDs
    
    # Persona stav
    current_emotional_state: EmotionalState = EmotionalState.NEUTRAL
    active_behavioral_traits: List[BehavioralTrait] = Field(default_factory=list)
    style_signature: str = Field(default="neutral")
    
    # Stylové vrstvy
    last_style_layer: Optional[Dict[str, Any]] = None
    emotional_history: List[Tuple[datetime, EmotionalState]] = Field(default_factory=list)
    style_evolution: List[Tuple[datetime, str]] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    interaction_count: int = Field(default=0)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ContextProcessor:
    """Procesor kontextu s podporou persona_core"""
    
    def __init__(self, memory_manager=None, trace_replayer=None):
        self.memory_manager = memory_manager
        self.trace_replayer = trace_replayer
        self.active_contexts: Dict[str, ContextState] = {}
        self.context_history: Dict[str, List[ContextState]] = {}
        
    async def initialize_context(
        self,
        persona_id: str,
        session_id: str,
        affective_profile: Optional[AffectiveProfile] = None
    ) -> ContextState:
        """Inicializuje nový kontext pro osobnost"""
        
        context_key = f"{persona_id}_{session_id}"
        
        # Vytvoření nového kontextu
        context = ContextState(
            persona_id=persona_id,
            session_id=session_id
        )
        
        # Nastavení z affective_profile pokud je poskytnut
        if affective_profile:
            context.current_emotional_state = affective_profile.primary_emotion
            context.active_behavioral_traits = affective_profile.primary_traits
            context.style_signature = affective_profile.get_style_signature()
            
            # Inicializace stylové vrstvy
            context.last_style_layer = {
                "emotional_intensity": affective_profile.current_affective_state.intensity,
                "valence": affective_profile.current_affective_state.valence,
                "arousal": affective_profile.current_affective_state.arousal,
                "dominance": affective_profile.current_affective_state.dominance,
                "communication_style": affective_profile.communication_style,
                "linguistic_markers": affective_profile.linguistic_markers
            }
        
        # Uložení do aktivních kontextů
        self.active_contexts[context_key] = context
        
        logger.info("Kontext inicializován",
                   persona_id=persona_id,
                   session_id=session_id,
                   style_signature=context.style_signature)
        
        return context
    
    async def update_context(
        self,
        persona_id: str,
        session_id: str,
        user_input: str,
        assistant_response: str,
        affective_profile: Optional[AffectiveProfile] = None,
        trace_data: Optional[Dict[str, Any]] = None
    ) -> ContextState:
        """Aktualizuje kontext po interakci"""
        
        context_key = f"{persona_id}_{session_id}"
        context = self.active_contexts.get(context_key)
        
        if not context:
            # Vytvoření nového kontextu pokud neexistuje
            context = await self.initialize_context(persona_id, session_id, affective_profile)
        
        # Aktualizace konverzace
        context.current_conversation.append({
            "role": "user",
            "content": user_input,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        context.current_conversation.append({
            "role": "assistant", 
            "content": assistant_response,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Omezení délky konverzace
        if len(context.current_conversation) > 20:
            context.current_conversation = context.current_conversation[-20:]
        
        # Aktualizace z affective_profile
        if affective_profile:
            # Sledování emoční evoluce
            if context.current_emotional_state != affective_profile.primary_emotion:
                context.emotional_history.append((
                    datetime.utcnow(),
                    affective_profile.primary_emotion
                ))
                context.current_emotional_state = affective_profile.primary_emotion
            
            # Sledování stylové evoluce
            new_style = affective_profile.get_style_signature()
            if context.style_signature != new_style:
                context.style_evolution.append((
                    datetime.utcnow(),
                    new_style
                ))
                context.style_signature = new_style
            
            # Aktualizace stylové vrstvy
            context.last_style_layer = {
                "emotional_intensity": affective_profile.current_affective_state.intensity,
                "valence": affective_profile.current_affective_state.valence,
                "arousal": affective_profile.current_affective_state.arousal,
                "dominance": affective_profile.current_affective_state.dominance,
                "communication_style": affective_profile.communication_style,
                "linguistic_markers": affective_profile.linguistic_markers,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            context.active_behavioral_traits = affective_profile.primary_traits
        
        # Záznam behaviorální stopy
        if self.trace_replayer and trace_data:
            await self._record_behavioral_trace(
                context, user_input, assistant_response, trace_data
            )
        
        # Aktualizace metadat
        context.interaction_count += 1
        context.updated_at = datetime.utcnow()
        
        # Uložení kontextu
        self.active_contexts[context_key] = context
        
        logger.debug("Kontext aktualizován",
                    persona_id=persona_id,
                    session_id=session_id,
                    interaction_count=context.interaction_count)
        
        return context
    
    async def get_context(self, persona_id: str, session_id: str) -> Optional[ContextState]:
        """Vrátí aktuální kontext"""
        context_key = f"{persona_id}_{session_id}"
        return self.active_contexts.get(context_key)
    
    async def get_relevant_memories(
        self,
        persona_id: str,
        session_id: str,
        query: str,
        max_memories: int = 5
    ) -> List[Memory]:
        """Vyhledá relevantní paměti pro kontext"""
        
        if not self.memory_manager:
            return []
        
        context = await self.get_context(persona_id, session_id)
        
        # Vytvoření dotazu s kontextovými informacemi
        memory_query = MemoryQuery(
            query_text=query,
            personality_id=persona_id,
            top_k=max_memories,
            similarity_threshold=0.7,
            memory_types=[MemoryType.EPISODIC, MemoryType.SEMANTIC, MemoryType.EMOTIONAL]
        )
        
        # Přidání kontextových filtrů
        if context:
            # Emoční kontext
            if context.current_emotional_state != EmotionalState.NEUTRAL:
                memory_query.filters = memory_query.filters or {}
                memory_query.filters["emotional_state"] = context.current_emotional_state.value
        
        try:
            search_results = await self.memory_manager.search_memories(memory_query)
            memories = [result.memory for result in search_results]
            
            # Aktualizace aktivních pamětí v kontextu
            if context:
                context.active_memories = [mem.id for mem in memories]
                context.updated_at = datetime.utcnow()
            
            return memories
            
        except Exception as e:
            logger.error("Chyba při vyhledávání pamětí", error=str(e))
            return []
    
    async def store_context_memory(
        self,
        persona_id: str,
        session_id: str,
        content: str,
        memory_type: MemoryType = MemoryType.EPISODIC,
        importance: float = 0.5
    ) -> Optional[Memory]:
        """Uloží kontext jako paměť"""
        
        if not self.memory_manager:
            return None
        
        context = await self.get_context(persona_id, session_id)
        
        # Vytvoření paměti s kontextovými metadaty
        metadata = {
            "session_id": session_id,
            "interaction_count": context.interaction_count if context else 0,
            "context_type": "conversation"
        }
        
        if context:
            metadata.update({
                "emotional_state": context.current_emotional_state.value,
                "style_signature": context.style_signature,
                "behavioral_traits": [trait.value for trait in context.active_behavioral_traits]
            })
        
        try:
            memory = Memory(
                id=f"ctx_{persona_id}_{session_id}_{datetime.utcnow().timestamp()}",
                personality_id=persona_id,
                content=content,
                memory_type=memory_type,
                importance=importance,
                metadata=metadata,
                created_at=datetime.utcnow()
            )
            
            await self.memory_manager.store_memory(memory)
            
            logger.debug("Kontextová paměť uložena",
                        memory_id=memory.id,
                        persona_id=persona_id)
            
            return memory
            
        except Exception as e:
            logger.error("Chyba při ukládání kontextové paměti", error=str(e))
            return None
    
    async def cleanup_old_contexts(self, max_age_hours: int = 24) -> int:
        """Vyčistí staré kontexty"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        # Přesun starých kontextů do historie
        to_remove = []
        for context_key, context in self.active_contexts.items():
            if context.updated_at < cutoff_time:
                # Přesun do historie
                persona_id = context.persona_id
                if persona_id not in self.context_history:
                    self.context_history[persona_id] = []
                
                self.context_history[persona_id].append(context)
                
                # Omezení historie
                if len(self.context_history[persona_id]) > 10:
                    self.context_history[persona_id] = self.context_history[persona_id][-10:]
                
                to_remove.append(context_key)
                cleaned_count += 1
        
        # Odstranění z aktivních kontextů
        for context_key in to_remove:
            del self.active_contexts[context_key]
        
        logger.info("Staré kontexty vyčištěny", cleaned_count=cleaned_count)
        return cleaned_count
    
    async def get_context_summary(self, persona_id: str, session_id: str) -> Dict[str, Any]:
        """Vrátí shrnutí kontextu"""
        
        context = await self.get_context(persona_id, session_id)
        if not context:
            return {}
        
        return {
            "persona_id": context.persona_id,
            "session_id": context.session_id,
            "interaction_count": context.interaction_count,
            "current_emotional_state": context.current_emotional_state.value,
            "style_signature": context.style_signature,
            "active_traits": [trait.value for trait in context.active_behavioral_traits],
            "conversation_length": len(context.current_conversation),
            "active_memories_count": len(context.active_memories),
            "emotional_changes": len(context.emotional_history),
            "style_changes": len(context.style_evolution),
            "last_updated": context.updated_at.isoformat(),
            "session_duration": (context.updated_at - context.created_at).total_seconds()
        }
    
    async def _record_behavioral_trace(
        self,
        context: ContextState,
        user_input: str,
        assistant_response: str,
        trace_data: Dict[str, Any]
    ):
        """Zaznamenává behaviorální stopu"""
        
        if not self.trace_replayer:
            return
        
        try:
            await self.trace_replayer.record_trace(
                persona_id=context.persona_id,
                trace_type=TraceType.CONVERSATION,
                input_stimulus=user_input,
                output_response=assistant_response,
                affective_profile=None,  # Bude předáno z volajícího kódu
                context={
                    "session_id": context.session_id,
                    "interaction_count": context.interaction_count,
                    "style_layer": context.last_style_layer,
                    **trace_data
                }
            )
        except Exception as e:
            logger.error("Chyba při záznamu behaviorální stopy", error=str(e))
