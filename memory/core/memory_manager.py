"""
Memory Manager - <PERSON><PERSON><PERSON><PERSON> třída pro správu paměti
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import structlog
import hashlib
import numpy as np

from .memory_types import Memory, MemoryType, MemoryQuery, MemorySearchResult, MemoryContext
from ..storage.postgres_storage import PostgresStorage
from ..storage.vector_storage import VectorStorage
from ..storage.cache_storage import CacheStorage

logger = structlog.get_logger()

class MemoryManager:
    """Hlavní třída pro správu paměti digitálních osobností"""

    def __init__(
        self,
        postgres_storage: PostgresStorage,
        vector_storage: VectorStorage,
        cache_storage: CacheStorage,
        embedding_model: str = "all-MiniLM-L6-v2"
    ):
        self.postgres_storage = postgres_storage
        self.vector_storage = vector_storage
        self.cache_storage = cache_storage
        self.embedding_model_name = embedding_model
        self.embedding_model = None

    async def initialize(self):
        """Inicializace memory manageru"""
        logger.info("Inicializace Memory Manager...")

        # Inicializace storage komponent
        await self.postgres_storage.initialize()
        await self.vector_storage.initialize()
        await self.cache_storage.initialize()

        # Pro demo účely používáme mock embedding
        self.embedding_model = "mock"
        logger.info("Mock embedding model inicializován")

        logger.info("Memory Manager inicializován")

    async def cleanup(self):
        """Úklid při ukončování"""
        logger.info("Ukončování Memory Manager...")
        await self.postgres_storage.cleanup()
        await self.vector_storage.cleanup()
        await self.cache_storage.cleanup()

    def _generate_embedding(self, text: str) -> List[float]:
        """Generování mock embedding pro text"""
        # Mock embedding založený na hash textu
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # Převod hash na čísla (384 dimenzí)
        embedding = []
        for i in range(0, min(len(text_hash), 384 * 2), 2):
            hex_pair = text_hash[i:i+2] if i+1 < len(text_hash) else text_hash[i] + '0'
            value = int(hex_pair, 16) / 255.0 - 0.5  # Normalizace na [-0.5, 0.5]
            embedding.append(value)

        # Doplnění do 384 dimenzí
        while len(embedding) < 384:
            embedding.append(0.0)

        # Normalizace
        norm = sum(x*x for x in embedding) ** 0.5
        if norm > 0:
            embedding = [x/norm for x in embedding]

        return embedding[:384]

    async def add_memory(
        self,
        content: str,
        memory_type: MemoryType,
        personality_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        importance: float = 0.5,
        context: Optional[MemoryContext] = None
    ) -> Memory:
        """Přidání nové paměti"""

        # Vytvoření embedding
        embedding = self._generate_embedding(content)

        # Vytvoření memory objektu
        memory = Memory(
            content=content,
            memory_type=memory_type,
            personality_id=personality_id,
            metadata=metadata or {},
            importance=importance,
            embedding=embedding
        )

        # Uložení do PostgreSQL
        await self.postgres_storage.store_memory(memory)

        # Uložení do vektorové databáze
        await self.vector_storage.store_embedding(
            memory_id=memory.id,
            embedding=embedding,
            metadata={
                "memory_type": memory_type.value,
                "personality_id": personality_id,
                "importance": importance,
                "created_at": memory.created_at.isoformat()
            }
        )

        # Cache pro rychlý přístup
        await self.cache_storage.cache_memory(memory)

        logger.info("Paměť přidána", memory_id=memory.id, type=memory_type.value)
        return memory

    async def get_memory(self, memory_id: str) -> Optional[Memory]:
        """Získání paměti podle ID"""

        # Nejprve zkusit cache
        memory = await self.cache_storage.get_memory(memory_id)
        if memory:
            # Aktualizace accessed_at
            memory.accessed_at = datetime.utcnow()
            await self.postgres_storage.update_memory(memory)
            return memory

        # Pokud není v cache, načíst z databáze
        memory = await self.postgres_storage.get_memory(memory_id)
        if memory:
            memory.accessed_at = datetime.utcnow()
            await self.postgres_storage.update_memory(memory)
            await self.cache_storage.cache_memory(memory)

        return memory

    async def query_memories(
        self,
        query: str,
        memory_types: Optional[List[MemoryType]] = None,
        personality_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        context: Optional[MemoryContext] = None
    ) -> List[MemorySearchResult]:
        """Vyhledávání pamětí podle dotazu"""

        # Generování embedding pro dotaz
        query_embedding = self._generate_embedding(query)

        # Vyhledávání v vektorové databázi
        vector_results = await self.vector_storage.search_similar(
            query_embedding=query_embedding,
            top_k=top_k * 2,  # Získáme více výsledků pro další filtrování
            similarity_threshold=similarity_threshold,
            metadata_filter={
                "memory_type": [mt.value for mt in memory_types] if memory_types else None,
                "personality_id": personality_id
            }
        )

        # Načtení úplných memory objektů
        results = []
        for vector_result in vector_results:
            memory = await self.get_memory(vector_result["memory_id"])
            if memory and memory.is_active:
                # Výpočet relevance skóre
                relevance_score = self._calculate_relevance(
                    memory=memory,
                    query=query,
                    similarity_score=vector_result["similarity"],
                    context=context
                )

                results.append(MemorySearchResult(
                    memory=memory,
                    similarity_score=vector_result["similarity"],
                    relevance_score=relevance_score,
                    context_match=self._check_context_match(memory, context)
                ))

        # Seřazení podle relevance a omezení na top_k
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:top_k]

    def _calculate_relevance(
        self,
        memory: Memory,
        query: str,
        similarity_score: float,
        context: Optional[MemoryContext] = None
    ) -> float:
        """Výpočet relevance skóre"""

        # Základní skóre je similarity
        relevance = similarity_score

        # Bonus za důležitost
        relevance += memory.importance * 0.2

        # Bonus za nedávný přístup
        if memory.accessed_at:
            days_since_access = (datetime.utcnow() - memory.accessed_at).days
            if days_since_access < 7:
                relevance += 0.1 * (7 - days_since_access) / 7

        # Bonus za kontext
        if context and self._check_context_match(memory, context):
            relevance += 0.15

        # Penalizace za stáří (pro krátkodobou paměť)
        if memory.memory_type == MemoryType.SHORT_TERM:
            days_old = (datetime.utcnow() - memory.created_at).days
            if days_old > 1:
                relevance -= 0.1 * days_old

        return min(1.0, max(0.0, relevance))

    def _check_context_match(
        self,
        memory: Memory,
        context: Optional[MemoryContext]
    ) -> bool:
        """Kontrola shody s kontextem"""
        if not context:
            return False

        # Kontrola personality_id
        if context.personality_id != memory.personality_id:
            return False

        # Kontrola tématu v metadatech
        if context.current_topic and "topic" in memory.metadata:
            return context.current_topic.lower() in memory.metadata["topic"].lower()

        return True

    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        importance: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Memory]:
        """Aktualizace paměti"""

        memory = await self.get_memory(memory_id)
        if not memory:
            return None

        # Aktualizace polí
        if content is not None:
            memory.content = content
            memory.embedding = self._generate_embedding(content)

        if importance is not None:
            memory.importance = importance

        if metadata is not None:
            memory.metadata.update(metadata)

        memory.updated_at = datetime.utcnow()

        # Uložení změn
        await self.postgres_storage.update_memory(memory)

        if content is not None:
            await self.vector_storage.update_embedding(
                memory_id=memory_id,
                embedding=memory.embedding,
                metadata={
                    "memory_type": memory.memory_type.value,
                    "personality_id": memory.personality_id,
                    "importance": memory.importance,
                    "updated_at": memory.updated_at.isoformat()
                }
            )

        await self.cache_storage.cache_memory(memory)

        logger.info("Paměť aktualizována", memory_id=memory_id)
        return memory

    async def delete_memory(self, memory_id: str) -> bool:
        """Smazání paměti"""

        # Soft delete - označení jako neaktivní
        memory = await self.get_memory(memory_id)
        if not memory:
            return False

        memory.is_active = False
        memory.updated_at = datetime.utcnow()

        await self.postgres_storage.update_memory(memory)
        await self.cache_storage.remove_memory(memory_id)

        logger.info("Paměť smazána", memory_id=memory_id)
        return True

    async def cleanup_expired_memories(self):
        """Úklid expirovaných pamětí"""

        expired_memories = await self.postgres_storage.get_expired_memories()

        for memory in expired_memories:
            await self.delete_memory(memory.id)

        logger.info("Úklid expirovaných pamětí dokončen", count=len(expired_memories))
