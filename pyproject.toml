[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "nestor"
version = "0.1.0"
description = "NESTOR: Platforma pro vytváření a tokenizaci digitálních osobností"
readme = "README.md"
license = {text = "BSD-3-Clause"}
authors = [
    {name = "NESTOR Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "NESTOR Development Team", email = "<EMAIL>"}
]
keywords = [
    "ai", "ml", "llm", "rag", "blockchain", "tokenization", 
    "digital-personalities", "microservices", "fastapi"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: BSD License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]
requires-python = ">=3.11"
dependencies = [
    # Core web framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # Database and ORM
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "psycopg2-binary>=2.9.7",
    "asyncpg>=0.29.0",
    
    # Redis and caching
    "redis>=5.0.0",
    "aioredis>=2.0.1",
    
    # Data validation and serialization
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
    
    # AI/ML libraries
    "sentence-transformers>=2.2.2",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    
    # Vector database
    "pgvector>=0.2.3",
    
    # Document processing
    "pypdf2>=3.0.1",
    "python-docx>=0.8.11",
    "python-multipart>=0.0.6",
    
    # HTTP client
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    
    # Logging and monitoring
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    
    # Security and authentication
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    
    # Configuration and environment
    "python-dotenv>=1.0.0",
    
    # Utilities
    "click>=8.1.7",
    "rich>=13.6.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # for testing FastAPI
    
    # Code quality
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
    
    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.23.0",
]

blockchain = [
    # Blockchain and Web3
    "web3>=6.11.0",
    "eth-account>=0.9.0",
    "solcx>=1.12.0",
]

llm-providers = [
    # LLM API clients
    "openai>=1.3.0",
    "anthropic>=0.7.0",
    "cohere>=4.32.0",
]

local-llm = [
    # Local LLM inference
    "llama-cpp-python>=0.2.11",
    "ollama>=0.1.7",
]

monitoring = [
    # Advanced monitoring
    "grafana-client>=3.5.0",
    "elasticsearch>=8.10.0",
]

all = [
    "nestor[dev,blockchain,llm-providers,local-llm,monitoring]"
]

[project.urls]
Homepage = "https://nestor.ai"
Documentation = "https://docs.nestor.ai"
Repository = "https://github.com/nestor-ai/nestor"
"Bug Tracker" = "https://github.com/nestor-ai/nestor/issues"
Changelog = "https://github.com/nestor-ai/nestor/blob/main/CHANGELOG.md"

[project.scripts]
nestor = "cli.main:app"

[tool.setuptools.packages.find]
where = ["."]
include = ["api*", "memory*", "vectorstore*", "llm_interface*", "tokenization*", "cli*"]
exclude = ["tests*", "docs*", "frontend*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | frontend
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["api", "memory", "vectorstore", "llm_interface", "tokenization", "cli"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "sentence_transformers.*",
    "transformers.*",
    "torch.*",
    "sklearn.*",
    "pgvector.*",
    "pypdf2.*",
    "docx.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["api", "memory", "vectorstore", "llm_interface", "tokenization", "cli"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "venv",
    ".eggs",
    "*.egg",
    "frontend",
]
