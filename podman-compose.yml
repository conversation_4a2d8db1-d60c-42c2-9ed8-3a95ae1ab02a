version: '3.8'

services:
  # PostgreSQL 15+ s pgvector r<PERSON><PERSON><PERSON><PERSON><PERSON>ím
  postgres:
    image: docker.io/pgvector/pgvector:pg15
    container_name: nestor-postgres
    environment:
      POSTGRES_DB: nestor
      POSTGRES_USER: nestor
      POSTGRES_PASSWORD: nestor_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nestor -d nestor"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # Redis pro cache a message broker
  redis:
    image: docker.io/library/redis:7-alpine
    container_name: nestor-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # Memory Context Processor (MCP)
  memory:
    build:
      context: ./memory
      dockerfile: Dockerfile
    container_name: nestor-mcp
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=*************************************************/nestor
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./memory:/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # RAG Service (Vectorstore)
  vectorstore:
    build:
      context: ./vectorstore
      dockerfile: Dockerfile
    container_name: nestor-rag
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=*************************************************/nestor
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./vectorstore:/app
      - ./data/samples:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # LLM Interface Service
  llm_interface:
    build:
      context: ./llm_interface
      dockerfile: Dockerfile
    container_name: nestor-llm
    ports:
      - "8003:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./llm_interface:/app
      - ./models:/app/models
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # Tokenization Service
  tokenization:
    build:
      context: ./tokenization
      dockerfile: Dockerfile
    container_name: nestor-tokenization
    ports:
      - "8004:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
      - BLOCKCHAIN_NETWORK=${BLOCKCHAIN_NETWORK:-ethereum}
      - BLOCKCHAIN_RPC_URL=${BLOCKCHAIN_RPC_URL:-}
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./tokenization:/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

  # Main API Service
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: nestor-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************/nestor
      - REDIS_URL=redis://redis:6379
      - MEMORY_SERVICE_URL=http://memory:8000
      - RAG_SERVICE_URL=http://vectorstore:8000
      - LLM_SERVICE_URL=http://llm_interface:8000
      - TOKENIZATION_SERVICE_URL=http://tokenization:8000
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      memory:
        condition: service_healthy
      vectorstore:
        condition: service_healthy
      llm_interface:
        condition: service_healthy
      tokenization:
        condition: service_healthy
    volumes:
      - ./api:/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - nestor-network

volumes:
  postgres_data:
  redis_data:

networks:
  nestor-network:
    driver: bridge
