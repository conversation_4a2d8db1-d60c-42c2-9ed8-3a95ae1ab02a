"""
Persona Dataset Builder - převádí vzpomínky a RAG extrahovaná data na LoRA-formátovaný dataset

Tento modul implementuje převod různých typů dat (JSON, RAG vzpomínky, konverzace)
do formátu vhodného pro trénink LoRA adaptérů pro digitální osobnosti.
"""

from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
import json
import structlog
import re
from pathlib import Path

from memory.core.memory_types import Memory, MemoryType
from persona_core.affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait

logger = structlog.get_logger()


class TrainingExample(BaseModel):
    """Jednotlivý tréninkovací příklad"""
    instruction: str = Field(..., description="Instrukce/prompt")
    input: str = Field(default="", description="Vstupní kontext")
    output: str = Field(..., description="Očekávaná odpověď")
    
    # Metadata pro LoRA
    style_signature: str = Field(..., description="Stylový podpis")
    emotional_context: str = Field(default="neutral")
    behavioral_traits: List[str] = Field(default_factory=list)
    
    # Kvalitativní metriky
    quality_score: float = Field(default=1.0, ge=0.0, le=1.0)
    complexity_level: int = Field(default=1, ge=1, le=5)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DatasetConfig(BaseModel):
    """Konfigurace pro generování datasetu"""
    
    # Základní nastavení
    min_examples: int = Field(default=50, ge=10)
    max_examples: int = Field(default=1000, ge=50)
    quality_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    
    # Formátování
    prompt_template: str = Field(default="### Instruction:\n{instruction}\n\n### Input:\n{input}\n\n### Response:\n{output}")
    include_system_prompt: bool = Field(default=True)
    system_prompt_template: str = Field(default="You are {persona_name}, a digital personality with the following traits: {traits}")
    
    # Augmentace dat
    enable_augmentation: bool = Field(default=True)
    augmentation_ratio: float = Field(default=0.3, ge=0.0, le=1.0)
    
    # Filtry
    min_input_length: int = Field(default=10)
    max_input_length: int = Field(default=500)
    min_output_length: int = Field(default=5)
    max_output_length: int = Field(default=300)


class PersonaDatasetBuilder:
    """Builder pro vytváření tréninkovacích datasetů z persona dat"""
    
    def __init__(self, config: Optional[DatasetConfig] = None):
        self.config = config or DatasetConfig()
        self.examples: List[TrainingExample] = []
        
    async def build_from_memories(
        self,
        memories: List[Memory],
        affective_profile: AffectiveProfile,
        conversation_context: Optional[List[Dict[str, str]]] = None
    ) -> List[TrainingExample]:
        """Vytvoří dataset z pamětí osobnosti"""
        
        logger.info("Vytváření datasetu z pamětí",
                   memory_count=len(memories),
                   persona_id=affective_profile.persona_id)
        
        examples = []
        style_signature = affective_profile.get_style_signature()
        
        for memory in memories:
            # Různé typy pamětí vyžadují různé přístupy
            if memory.memory_type == MemoryType.EPISODIC:
                examples.extend(await self._process_episodic_memory(memory, affective_profile))
            elif memory.memory_type == MemoryType.SEMANTIC:
                examples.extend(await self._process_semantic_memory(memory, affective_profile))
            elif memory.memory_type == MemoryType.EMOTIONAL:
                examples.extend(await self._process_emotional_memory(memory, affective_profile))
            elif memory.memory_type == MemoryType.PROCEDURAL:
                examples.extend(await self._process_procedural_memory(memory, affective_profile))
        
        # Přidání konverzačního kontextu
        if conversation_context:
            examples.extend(await self._process_conversation_context(
                conversation_context, affective_profile
            ))
        
        # Filtrace a validace
        filtered_examples = self._filter_examples(examples)
        
        # Augmentace dat pokud je povolena
        if self.config.enable_augmentation:
            augmented_examples = await self._augment_examples(filtered_examples, affective_profile)
            filtered_examples.extend(augmented_examples)
        
        logger.info("Dataset vytvořen",
                   total_examples=len(filtered_examples),
                   original_count=len(examples))
        
        return filtered_examples
    
    async def build_from_json(
        self,
        json_data: Union[str, Dict, List[Dict]],
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Vytvoří dataset z JSON dat"""
        
        if isinstance(json_data, str):
            data = json.loads(json_data)
        else:
            data = json_data
        
        if not isinstance(data, list):
            data = [data]
        
        examples = []
        style_signature = affective_profile.get_style_signature()
        
        for item in data:
            example = await self._process_json_item(item, affective_profile)
            if example:
                examples.append(example)
        
        return self._filter_examples(examples)
    
    async def build_from_conversations(
        self,
        conversations: List[List[Dict[str, str]]],
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Vytvoří dataset z konverzací"""
        
        examples = []
        
        for conversation in conversations:
            conv_examples = await self._process_conversation(conversation, affective_profile)
            examples.extend(conv_examples)
        
        return self._filter_examples(examples)
    
    async def _process_episodic_memory(
        self,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Zpracuje epizodickou paměť"""
        
        examples = []
        
        # Základní vzpomínka jako story-telling
        instruction = "Vyprávěj o této vzpomínce ze svého života:"
        input_text = f"Vzpomínka: {memory.content[:100]}..."
        output = memory.content
        
        example = TrainingExample(
            instruction=instruction,
            input=input_text,
            output=output,
            style_signature=affective_profile.get_style_signature(),
            emotional_context=affective_profile.primary_emotion.value,
            behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
            quality_score=memory.importance
        )
        examples.append(example)
        
        # Generování otázek o vzpomínce
        questions = self._generate_memory_questions(memory.content)
        for question in questions:
            answer = await self._generate_memory_answer(question, memory, affective_profile)
            if answer:
                example = TrainingExample(
                    instruction=question,
                    input="",
                    output=answer,
                    style_signature=affective_profile.get_style_signature(),
                    emotional_context=affective_profile.primary_emotion.value,
                    behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
                    quality_score=memory.importance * 0.8
                )
                examples.append(example)
        
        return examples
    
    async def _process_semantic_memory(
        self,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Zpracuje sémantickou paměť (fakta, znalosti)"""
        
        examples = []
        
        # Faktické otázky
        instruction = "Odpověz na základě svých znalostí:"
        
        # Extrakce klíčových faktů
        facts = self._extract_facts_from_content(memory.content)
        
        for fact in facts:
            question = self._generate_fact_question(fact)
            if question:
                example = TrainingExample(
                    instruction=instruction,
                    input=question,
                    output=fact,
                    style_signature=affective_profile.get_style_signature(),
                    emotional_context=affective_profile.primary_emotion.value,
                    behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
                    quality_score=memory.importance
                )
                examples.append(example)
        
        return examples
    
    async def _process_emotional_memory(
        self,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Zpracuje emoční paměť"""
        
        examples = []
        
        # Emoční reakce
        instruction = "Jak se cítíš v této situaci?"
        input_text = memory.content
        
        # Generování emoční odpovědi
        emotional_response = await self._generate_emotional_response(memory, affective_profile)
        
        example = TrainingExample(
            instruction=instruction,
            input=input_text,
            output=emotional_response,
            style_signature=affective_profile.get_style_signature(),
            emotional_context=affective_profile.primary_emotion.value,
            behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
            quality_score=memory.importance,
            complexity_level=3
        )
        examples.append(example)
        
        return examples
    
    async def _process_procedural_memory(
        self,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Zpracuje procedurální paměť (dovednosti, postupy)"""
        
        examples = []
        
        # Instrukční příklady
        instruction = "Vysvětli, jak se to dělá:"
        
        # Extrakce kroků z obsahu
        steps = self._extract_procedure_steps(memory.content)
        
        if steps:
            example = TrainingExample(
                instruction=instruction,
                input=f"Postup: {memory.content[:50]}...",
                output=memory.content,
                style_signature=affective_profile.get_style_signature(),
                emotional_context=affective_profile.primary_emotion.value,
                behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
                quality_score=memory.importance,
                complexity_level=2
            )
            examples.append(example)
        
        return examples
    
    async def _process_conversation_context(
        self,
        conversation: List[Dict[str, str]],
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Zpracuje konverzační kontext"""
        
        examples = []
        
        for i in range(1, len(conversation)):
            if conversation[i].get("role") == "assistant":
                # Vytvoření tréninkovacího příkladu z konverzace
                context = conversation[:i]
                user_message = conversation[i-1].get("content", "")
                assistant_response = conversation[i].get("content", "")
                
                # Formátování kontextu
                context_str = "\n".join([
                    f"{msg['role']}: {msg['content']}" 
                    for msg in context[-3:]  # Posledních 3 zpráv
                ])
                
                example = TrainingExample(
                    instruction="Odpověz v konverzaci jako digitální osobnost:",
                    input=f"Kontext:\n{context_str}\n\nUživatel: {user_message}",
                    output=assistant_response,
                    style_signature=affective_profile.get_style_signature(),
                    emotional_context=affective_profile.primary_emotion.value,
                    behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
                    quality_score=0.9,
                    complexity_level=2
                )
                examples.append(example)
        
        return examples
    
    async def _process_json_item(
        self,
        item: Dict[str, Any],
        affective_profile: AffectiveProfile
    ) -> Optional[TrainingExample]:
        """Zpracuje jednotlivou JSON položku"""
        
        # Očekávané formáty
        if "instruction" in item and "output" in item:
            return TrainingExample(
                instruction=item["instruction"],
                input=item.get("input", ""),
                output=item["output"],
                style_signature=affective_profile.get_style_signature(),
                emotional_context=item.get("emotional_context", affective_profile.primary_emotion.value),
                behavioral_traits=item.get("behavioral_traits", [trait.value for trait in affective_profile.primary_traits]),
                quality_score=item.get("quality_score", 1.0)
            )
        
        elif "prompt" in item and "response" in item:
            return TrainingExample(
                instruction=item["prompt"],
                input="",
                output=item["response"],
                style_signature=affective_profile.get_style_signature(),
                emotional_context=affective_profile.primary_emotion.value,
                behavioral_traits=[trait.value for trait in affective_profile.primary_traits],
                quality_score=item.get("quality", 1.0)
            )
        
        return None
    
    def _filter_examples(self, examples: List[TrainingExample]) -> List[TrainingExample]:
        """Filtruje příklady podle kvality a délky"""
        
        filtered = []
        
        for example in examples:
            # Kontrola délky
            if (len(example.input) < self.config.min_input_length or 
                len(example.input) > self.config.max_input_length):
                continue
            
            if (len(example.output) < self.config.min_output_length or 
                len(example.output) > self.config.max_output_length):
                continue
            
            # Kontrola kvality
            if example.quality_score < self.config.quality_threshold:
                continue
            
            filtered.append(example)
        
        # Omezení počtu příkladů
        if len(filtered) > self.config.max_examples:
            # Seřazení podle kvality a výběr nejlepších
            filtered.sort(key=lambda x: x.quality_score, reverse=True)
            filtered = filtered[:self.config.max_examples]
        
        return filtered
    
    async def _augment_examples(
        self,
        examples: List[TrainingExample],
        affective_profile: AffectiveProfile
    ) -> List[TrainingExample]:
        """Augmentuje existující příklady"""
        
        augmented = []
        target_count = int(len(examples) * self.config.augmentation_ratio)
        
        for i in range(target_count):
            original = examples[i % len(examples)]
            
            # Parafráze instrukce
            paraphrased_instruction = await self._paraphrase_instruction(original.instruction)
            
            # Variace odpovědi
            varied_output = await self._vary_response(original.output, affective_profile)
            
            augmented_example = TrainingExample(
                instruction=paraphrased_instruction,
                input=original.input,
                output=varied_output,
                style_signature=original.style_signature,
                emotional_context=original.emotional_context,
                behavioral_traits=original.behavioral_traits,
                quality_score=original.quality_score * 0.9  # Mírně nižší kvalita
            )
            augmented.append(augmented_example)
        
        return augmented
    
    def _generate_memory_questions(self, content: str) -> List[str]:
        """Generuje otázky o vzpomínce"""
        questions = [
            "Co se stalo?",
            "Jak ses při tom cítila?",
            "Co si o tom myslíš?",
            "Proč je to pro tebe důležité?",
            "Co ses z toho naučila?"
        ]
        return questions[:3]  # Omezení na 3 otázky
    
    async def _generate_memory_answer(
        self,
        question: str,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> Optional[str]:
        """Generuje odpověď na otázku o vzpomínce"""
        
        # Jednoduchá template-based generace
        if "co se stalo" in question.lower():
            return f"Stalo se to: {memory.content[:100]}..."
        elif "jak ses cítila" in question.lower():
            return f"Cítila jsem se {affective_profile.primary_emotion.value}..."
        elif "co si myslíš" in question.lower():
            return f"Myslím si, že {memory.content[:50]}..."
        
        return None
    
    def _extract_facts_from_content(self, content: str) -> List[str]:
        """Extrahuje fakta z obsahu"""
        # Jednoduchá extrakce vět
        sentences = re.split(r'[.!?]+', content)
        return [s.strip() for s in sentences if len(s.strip()) > 10][:3]
    
    def _generate_fact_question(self, fact: str) -> Optional[str]:
        """Generuje otázku k faktu"""
        if len(fact) > 20:
            return f"Co víš o {fact[:30]}...?"
        return None
    
    async def _generate_emotional_response(
        self,
        memory: Memory,
        affective_profile: AffectiveProfile
    ) -> str:
        """Generuje emoční odpověď"""
        
        emotion = affective_profile.primary_emotion
        
        templates = {
            EmotionalState.JOY: "Cítím radost a štěstí...",
            EmotionalState.SADNESS: "Je mi smutno a cítím melancholii...",
            EmotionalState.CONTEMPLATIVE: "Zamýšlím se nad tím...",
            EmotionalState.PLAYFUL: "Je to zábavné a těším se...",
        }
        
        return templates.get(emotion, "Mám smíšené pocity...")
    
    def _extract_procedure_steps(self, content: str) -> List[str]:
        """Extrahuje kroky z procedurálního obsahu"""
        # Hledání číslovaných kroků nebo bullet pointů
        steps = re.findall(r'(?:\d+\.|[-*])\s*(.+)', content)
        return steps[:5]  # Max 5 kroků
    
    async def _paraphrase_instruction(self, instruction: str) -> str:
        """Parafrazuje instrukci"""
        # Jednoduchá parafráze
        paraphrases = {
            "Vyprávěj": "Řekni mi",
            "Odpověz": "Popiš",
            "Vysvětli": "Ukaž mi",
            "Jak se cítíš": "Jaké máš pocity"
        }
        
        for original, replacement in paraphrases.items():
            if original in instruction:
                return instruction.replace(original, replacement)
        
        return instruction
    
    async def _vary_response(self, response: str, affective_profile: AffectiveProfile) -> str:
        """Vytvoří variaci odpovědi"""
        # Přidání stylových markerů
        if affective_profile.primary_emotion == EmotionalState.PLAYFUL:
            return response + " 😊"
        elif affective_profile.primary_emotion == EmotionalState.CONTEMPLATIVE:
            return response + "..."
        
        return response
    
    def export_to_jsonl(self, examples: List[TrainingExample], output_path: str) -> None:
        """Exportuje příklady do JSONL formátu"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            for example in examples:
                # Formátování podle konfigurace
                formatted_text = self.config.prompt_template.format(
                    instruction=example.instruction,
                    input=example.input,
                    output=example.output
                )
                
                json_obj = {
                    "text": formatted_text,
                    "style_signature": example.style_signature,
                    "emotional_context": example.emotional_context,
                    "behavioral_traits": example.behavioral_traits,
                    "quality_score": example.quality_score
                }
                
                f.write(json.dumps(json_obj, ensure_ascii=False) + '\n')
        
        logger.info("Dataset exportován", path=output_path, examples=len(examples))
