"""
Dream Prompter - p<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>namy typu "memory trace" nebo "symbolická vzpomínka"
do tréninkových příkladů a stylizuje výstupy dle osobnosti

Tento modul implementuje pokročilé techniky pro generování snov<PERSON>, symbolických
a stylizovaných promptů pro trénink LoRA adaptérů.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import structlog
import random
import re

from persona_core.affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait
from persona_core.trace_replayer import BehavioralTrace, TraceType

logger = structlog.get_logger()


class DreamType(str, Enum):
    """Typy snových promptů"""
    SYMBOLIC = "symbolic"          # Symbolické vzpomínky
    METAPHORICAL = "metaphorical"  # Metaforické vyjádření
    EMOTIONAL = "emotional"        # Emoční ladění
    NARRATIVE = "narrative"        # Narativní styl
    POETIC = "poetic"             # Poetické vyjádření
    STREAM_OF_CONSCIOUSNESS = "stream_of_consciousness"  # Proud vědomí


class StyleIntensity(str, Enum):
    """Intenzita stylizace"""
    SUBTLE = "subtle"      # Jemná stylizace
    MODERATE = "moderate"  # Střední stylizace
    STRONG = "strong"      # Silná stylizace
    EXTREME = "extreme"    # Extrémní stylizace


class DreamPrompt(BaseModel):
    """Snový prompt pro trénink"""

    # Základní struktura
    original_content: str = Field(..., description="Původní obsah")
    dreamed_content: str = Field(..., description="Stylizovaný obsah")

    # Stylové parametry
    dream_type: DreamType
    style_intensity: StyleIntensity
    style_signature: str

    # Emoční kontext
    emotional_coloring: EmotionalState
    emotional_intensity: float = Field(ge=0.0, le=1.0)

    # Behaviorální rysy
    active_traits: List[BehavioralTrait] = Field(default_factory=list)
    trait_weights: Dict[BehavioralTrait, float] = Field(default_factory=dict)

    # Metadata
    symbolic_elements: List[str] = Field(default_factory=list)
    metaphors_used: List[str] = Field(default_factory=list)
    linguistic_devices: List[str] = Field(default_factory=list)

    # Kvalitativní metriky
    creativity_score: float = Field(default=0.5, ge=0.0, le=1.0)
    coherence_score: float = Field(default=0.5, ge=0.0, le=1.0)
    style_consistency: float = Field(default=0.5, ge=0.0, le=1.0)


class DreamPrompter:
    """Generátor snových a stylizovaných promptů"""

    def __init__(self):
        self.style_templates = self._load_style_templates()
        self.symbolic_dictionary = self._load_symbolic_dictionary()
        self.emotional_modifiers = self._load_emotional_modifiers()

    async def create_dream_prompt(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        dream_type: DreamType = DreamType.SYMBOLIC,
        intensity: StyleIntensity = StyleIntensity.MODERATE
    ) -> DreamPrompt:
        """Vytvoří snový prompt z obsahu"""

        logger.debug("Vytváření snového promptu",
                    dream_type=dream_type.value,
                    intensity=intensity.value,
                    content_length=len(content))

        # Analýza původního obsahu
        content_analysis = await self._analyze_content(content)

        # Aplikace stylizace podle typu
        if dream_type == DreamType.SYMBOLIC:
            dreamed_content = await self._apply_symbolic_transformation(
                content, affective_profile, intensity
            )
        elif dream_type == DreamType.METAPHORICAL:
            dreamed_content = await self._apply_metaphorical_transformation(
                content, affective_profile, intensity
            )
        elif dream_type == DreamType.EMOTIONAL:
            dreamed_content = await self._apply_emotional_transformation(
                content, affective_profile, intensity
            )
        elif dream_type == DreamType.NARRATIVE:
            dreamed_content = await self._apply_narrative_transformation(
                content, affective_profile, intensity
            )
        elif dream_type == DreamType.POETIC:
            dreamed_content = await self._apply_poetic_transformation(
                content, affective_profile, intensity
            )
        else:  # STREAM_OF_CONSCIOUSNESS
            dreamed_content = await self._apply_consciousness_stream(
                content, affective_profile, intensity
            )

        # Extrakce použitých prvků
        symbolic_elements = self._extract_symbolic_elements(dreamed_content)
        metaphors = self._extract_metaphors(dreamed_content)
        linguistic_devices = self._extract_linguistic_devices(dreamed_content)

        # Výpočet metrik
        creativity = self._calculate_creativity_score(content, dreamed_content)
        coherence = self._calculate_coherence_score(dreamed_content)
        consistency = self._calculate_style_consistency(dreamed_content, affective_profile)

        dream_prompt = DreamPrompt(
            original_content=content,
            dreamed_content=dreamed_content,
            dream_type=dream_type,
            style_intensity=intensity,
            style_signature=affective_profile.get_style_signature(),
            emotional_coloring=affective_profile.primary_emotion,
            emotional_intensity=affective_profile.current_affective_state.intensity,
            active_traits=affective_profile.primary_traits,
            trait_weights=affective_profile.trait_weights,
            symbolic_elements=symbolic_elements,
            metaphors_used=metaphors,
            linguistic_devices=linguistic_devices,
            creativity_score=creativity,
            coherence_score=coherence,
            style_consistency=consistency
        )

        logger.debug("Snový prompt vytvořen",
                    creativity=creativity,
                    coherence=coherence,
                    consistency=consistency)

        return dream_prompt

    async def stylize_for_persona(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        target_style: Optional[str] = None
    ) -> str:
        """Stylizuje obsah podle osobnosti"""

        # Určení cílového stylu
        if not target_style:
            target_style = self._determine_optimal_style(affective_profile)

        # Aplikace stylových transformací
        styled_content = content

        # Emoční ladění
        styled_content = await self._apply_emotional_coloring(
            styled_content, affective_profile.primary_emotion
        )

        # Behaviorální rysy
        for trait in affective_profile.primary_traits:
            weight = affective_profile.trait_weights.get(trait, 0.5)
            styled_content = await self._apply_trait_styling(
                styled_content, trait, weight
            )

        # Jazykové markery
        styled_content = await self._apply_linguistic_markers(
            styled_content, affective_profile.linguistic_markers
        )

        # Finální úpravy podle komunikačního stylu
        styled_content = await self._apply_communication_style(
            styled_content, affective_profile.communication_style
        )

        return styled_content

    async def create_training_examples_from_traces(
        self,
        traces: List[BehavioralTrace],
        affective_profile: AffectiveProfile,
        examples_per_trace: int = 3
    ) -> List[Dict[str, str]]:
        """Vytvoří tréninkovací příklady z behaviorálních stop"""

        examples = []

        for trace in traces:
            # Základní příklad z trace
            base_example = {
                "instruction": f"Odpověz jako {affective_profile.name}:",
                "input": trace.input_stimulus,
                "output": trace.output_response,
                "style_signature": trace.style_signature,
                "emotional_context": trace.emotional_state_before.value
            }
            examples.append(base_example)

            # Generování variant
            for i in range(examples_per_trace - 1):
                variant = await self._create_trace_variant(trace, affective_profile, i)
                if variant:
                    examples.append(variant)

        return examples

    def _load_style_templates(self) -> Dict[str, List[str]]:
        """Načte stylové šablony"""
        return {
            "submissive-dreamy": [
                "Možná by se dalo říct...",
                "Zdá se mi, že...",
                "Myslím si, že...",
                "Cítím, že..."
            ],
            "dominant-practical": [
                "Je jasné, že...",
                "Určitě...",
                "Bez pochyby...",
                "Rozhodně..."
            ],
            "romantic-sensual": [
                "S láskou v srdci...",
                "Něžně bych řekla...",
                "S vášní cítím...",
                "Romanticky..."
            ],
            "intellectual-analytical": [
                "Logicky vzato...",
                "Analýzou dospívám...",
                "Racionálně...",
                "Objektivně..."
            ]
        }

    def _load_symbolic_dictionary(self) -> Dict[str, List[str]]:
        """Načte slovník symbolů"""
        return {
            "emotions": {
                "joy": ["slunce", "květiny", "tanec", "světlo"],
                "sadness": ["déšť", "stíny", "podzim", "mlha"],
                "love": ["růže", "srdce", "hvězdy", "objetí"],
                "fear": ["temnota", "bouře", "labyrint", "propast"]
            },
            "nature": ["stromy", "řeka", "hory", "moře", "vítr", "země"],
            "time": ["hodiny", "roční období", "vlny", "cykly"],
            "transformation": ["motýl", "phoenix", "metamorfóza", "alchymie"]
        }

    def _load_emotional_modifiers(self) -> Dict[EmotionalState, Dict[str, List[str]]]:
        """Načte emoční modifikátory"""
        return {
            EmotionalState.JOY: {
                "adjectives": ["zářivý", "radostný", "světlý", "hřejivý"],
                "verbs": ["tančit", "zpívat", "zářit", "objímat"],
                "adverbs": ["radostně", "vesele", "nadšeně", "hřejivě"]
            },
            EmotionalState.SADNESS: {
                "adjectives": ["smutný", "melancholický", "tichý", "zamyšlený"],
                "verbs": ["plakat", "vzpomínat", "snít", "toužit"],
                "adverbs": ["smutně", "tiše", "melancholicky", "nostalgicky"]
            },
            EmotionalState.CONTEMPLATIVE: {
                "adjectives": ["zamyšlený", "hluboký", "moudrý", "klidný"],
                "verbs": ["přemýšlet", "uvažovat", "meditovat", "pozorovat"],
                "adverbs": ["zamyšleně", "hluboce", "klidně", "moudře"]
            }
        }

    async def _analyze_content(self, content: str) -> Dict[str, Any]:
        """Analyzuje obsah pro stylizaci"""
        return {
            "length": len(content),
            "sentences": len(re.split(r'[.!?]+', content)),
            "emotional_words": self._count_emotional_words(content),
            "complexity": self._assess_complexity(content),
            "themes": self._extract_themes(content)
        }

    async def _apply_symbolic_transformation(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje symbolickou transformaci"""

        # Základní symbolická náhrada
        symbolic_content = content

        # Nahrazení emočních slov symboly
        emotion = affective_profile.primary_emotion
        if emotion in self.symbolic_dictionary["emotions"]:
            symbols = self.symbolic_dictionary["emotions"][emotion.value]

            # Náhrada podle intenzity
            if intensity in [StyleIntensity.STRONG, StyleIntensity.EXTREME]:
                symbolic_content = self._replace_with_symbols(symbolic_content, symbols)

        # Přidání symbolických prvků
        if intensity != StyleIntensity.SUBTLE:
            symbolic_content = self._add_symbolic_elements(symbolic_content, affective_profile)

        return symbolic_content

    async def _apply_metaphorical_transformation(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje metaforickou transformaci"""

        metaphorical_content = content

        # Přidání metafor podle emočního stavu
        emotion = affective_profile.primary_emotion

        if emotion == EmotionalState.JOY:
            metaphorical_content = f"Jako slunce prosvětlující den, {metaphorical_content.lower()}"
        elif emotion == EmotionalState.SADNESS:
            metaphorical_content = f"Jako déšť padající na zem, {metaphorical_content.lower()}"
        elif emotion == EmotionalState.CONTEMPLATIVE:
            metaphorical_content = f"Jako hluboké vody jezera, {metaphorical_content.lower()}"

        return metaphorical_content

    async def _apply_emotional_transformation(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje emoční transformaci"""

        emotion = affective_profile.primary_emotion
        emotional_content = content

        # Přidání emočních modifikátorů
        if emotion in self.emotional_modifiers:
            modifiers = self.emotional_modifiers[emotion]

            # Přidání příslovcí
            if intensity in [StyleIntensity.MODERATE, StyleIntensity.STRONG]:
                adverb = random.choice(modifiers["adverbs"])
                emotional_content = f"{adverb.capitalize()} {emotional_content.lower()}"

        return emotional_content

    async def _apply_narrative_transformation(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje narativní transformaci"""

        # Převod do první osoby a narativního stylu
        narrative_content = content

        # Přidání narativních prvků
        if "jsem" not in narrative_content.lower():
            narrative_content = f"Vzpomínám si, jak {narrative_content.lower()}"

        # Přidání časových markerů
        if intensity != StyleIntensity.SUBTLE:
            narrative_content = f"Bylo to v době, kdy {narrative_content.lower()}"

        return narrative_content

    async def _apply_poetic_transformation(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje poetickou transformaci"""

        poetic_content = content

        # Přidání poetických prvků
        if intensity in [StyleIntensity.STRONG, StyleIntensity.EXTREME]:
            # Rytmus a rým (zjednodušeně)
            poetic_content = self._add_poetic_rhythm(poetic_content)

        # Metafory a obraznost
        poetic_content = self._enhance_imagery(poetic_content, affective_profile)

        return poetic_content

    async def _apply_consciousness_stream(
        self,
        content: str,
        affective_profile: AffectiveProfile,
        intensity: StyleIntensity
    ) -> str:
        """Aplikuje proud vědomí"""

        # Fragmentace a asociativní myšlení
        stream_content = content

        # Přidání asociativních prvků
        associations = self._generate_associations(content, affective_profile)

        if associations and intensity != StyleIntensity.SUBTLE:
            stream_content = f"{content}... {' '.join(associations[:3])}..."

        return stream_content

    def _count_emotional_words(self, content: str) -> int:
        """Spočítá emoční slova v obsahu"""
        emotional_words = ["cítím", "láska", "radost", "smutek", "strach", "štěstí"]
        return sum(1 for word in emotional_words if word in content.lower())

    def _assess_complexity(self, content: str) -> float:
        """Hodnotí složitost obsahu"""
        words = content.split()
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        return min(1.0, avg_word_length / 10.0)

    def _extract_themes(self, content: str) -> List[str]:
        """Extrahuje témata z obsahu"""
        themes = []
        if any(word in content.lower() for word in ["láska", "vztah", "srdce"]):
            themes.append("love")
        if any(word in content.lower() for word in ["příroda", "strom", "květina"]):
            themes.append("nature")
        if any(word in content.lower() for word in ["čas", "vzpomínka", "minulost"]):
            themes.append("time")
        return themes

    def _replace_with_symbols(self, content: str, symbols: List[str]) -> str:
        """Nahradí slova symboly"""
        # Jednoduchá náhrada - v reálné implementaci by byla sofistikovanější
        if symbols:
            symbol = random.choice(symbols)
            return f"{content} (jako {symbol})"
        return content

    def _add_symbolic_elements(self, content: str, affective_profile: AffectiveProfile) -> str:
        """Přidá symbolické prvky"""
        nature_symbols = self.symbolic_dictionary["nature"]
        if nature_symbols:
            symbol = random.choice(nature_symbols)
            return f"{content}, připomíná mi to {symbol}"
        return content

    def _add_poetic_rhythm(self, content: str) -> str:
        """Přidá poetický rytmus"""
        # Zjednodušená implementace
        return content.replace(".", ",\n")

    def _enhance_imagery(self, content: str, affective_profile: AffectiveProfile) -> str:
        """Vylepší obraznost"""
        emotion = affective_profile.primary_emotion
        if emotion == EmotionalState.JOY:
            return f"{content}, jako by se svět rozzářil"
        elif emotion == EmotionalState.SADNESS:
            return f"{content}, v šedivém světle melancholie"
        return content

    def _generate_associations(self, content: str, affective_profile: AffectiveProfile) -> List[str]:
        """Generuje asociace pro proud vědomí"""
        associations = []

        # Asociace podle emočního stavu
        emotion = affective_profile.primary_emotion
        if emotion == EmotionalState.CONTEMPLATIVE:
            associations = ["hluboké myšlenky", "tiché chvíle", "vnitřní klid"]
        elif emotion == EmotionalState.PLAYFUL:
            associations = ["smích", "radost", "hravost"]

        return associations

    def _extract_symbolic_elements(self, content: str) -> List[str]:
        """Extrahuje symbolické prvky z obsahu"""
        symbols = []
        for category, symbol_list in self.symbolic_dictionary.items():
            if isinstance(symbol_list, list):
                for symbol in symbol_list:
                    if symbol in content.lower():
                        symbols.append(symbol)
        return symbols

    def _extract_metaphors(self, content: str) -> List[str]:
        """Extrahuje metafory z obsahu"""
        metaphor_patterns = [r"jako (.+?),", r"připomíná (.+?),", r"podobně (.+?),"]
        metaphors = []

        for pattern in metaphor_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            metaphors.extend(matches)

        return metaphors

    def _extract_linguistic_devices(self, content: str) -> List[str]:
        """Extrahuje jazykové prostředky"""
        devices = []

        if "..." in content:
            devices.append("ellipsis")
        if content.count(",") > 2:
            devices.append("enumeration")
        if any(word in content.lower() for word in ["jako", "podobně", "připomíná"]):
            devices.append("simile")

        return devices

    def _calculate_creativity_score(self, original: str, transformed: str) -> float:
        """Vypočítá skóre kreativity"""
        # Jednoduchá metrika - poměr změn
        if len(original) == 0:
            return 0.0

        changes = abs(len(transformed) - len(original))
        return min(1.0, changes / len(original))

    def _calculate_coherence_score(self, content: str) -> float:
        """Vypočítá skóre koherence"""
        # Jednoduchá metrika - přítomnost spojovacích slov
        connectors = ["a", "ale", "proto", "protože", "když", "pokud"]
        connector_count = sum(1 for conn in connectors if conn in content.lower())

        sentences = len(re.split(r'[.!?]+', content))
        if sentences <= 1:
            return 1.0

        return min(1.0, connector_count / (sentences - 1))

    def _calculate_style_consistency(self, content: str, affective_profile: AffectiveProfile) -> float:
        """Vypočítá konzistenci stylu"""
        # Kontrola přítomnosti stylových markerů
        style_markers = 0
        total_markers = 0

        # Emoční markery
        emotion = affective_profile.primary_emotion
        if emotion in self.emotional_modifiers:
            modifiers = self.emotional_modifiers[emotion]
            for category, words in modifiers.items():
                total_markers += len(words)
                for word in words:
                    if word in content.lower():
                        style_markers += 1

        return style_markers / max(1, total_markers)

    def _determine_optimal_style(self, affective_profile: AffectiveProfile) -> str:
        """Určí optimální styl pro osobnost"""

        # Kombinace dominantních rysů
        traits = affective_profile.primary_traits[:2]  # První dva rysy
        emotion = affective_profile.primary_emotion

        if BehavioralTrait.DREAMY in traits and emotion == EmotionalState.CONTEMPLATIVE:
            return "dreamy-contemplative"
        elif BehavioralTrait.ROMANTIC in traits and emotion == EmotionalState.JOY:
            return "romantic-joyful"
        elif BehavioralTrait.INTELLECTUAL in traits:
            return "intellectual-analytical"
        else:
            return "balanced-neutral"

    async def _apply_emotional_coloring(self, content: str, emotion: EmotionalState) -> str:
        """Aplikuje emoční zabarvení"""

        if emotion == EmotionalState.MELANCHOLIC:
            return f"{content}... *vzdychne si*"
        elif emotion == EmotionalState.PLAYFUL:
            return f"{content} *usmívá se*"
        elif emotion == EmotionalState.CONTEMPLATIVE:
            return f"*zamyšleně* {content}"

        return content

    async def _apply_trait_styling(self, content: str, trait: BehavioralTrait, weight: float) -> str:
        """Aplikuje stylizaci podle behaviorálního rysu"""

        if weight < 0.3:
            return content  # Slabý vliv

        if trait == BehavioralTrait.SUBMISSIVE:
            return f"Myslím, že {content.lower()}"
        elif trait == BehavioralTrait.DOMINANT:
            return content.replace(".", "!")
        elif trait == BehavioralTrait.DREAMY:
            return f"{content}..."
        elif trait == BehavioralTrait.ROMANTIC:
            return f"S láskou v srdci, {content.lower()}"

        return content

    async def _apply_linguistic_markers(self, content: str, markers: List[str]) -> str:
        """Aplikuje jazykové markery"""

        styled_content = content

        for marker in markers[:2]:  # Max 2 markery
            if marker == "ellipsis":
                styled_content = styled_content.replace(".", "...")
            elif marker == "emphasis":
                styled_content = f"*{styled_content}*"

        return styled_content

    async def _apply_communication_style(self, content: str, style: str) -> str:
        """Aplikuje komunikační styl"""

        if style == "formal":
            return content.replace("jsem", "jsem si vědom/a")
        elif style == "casual":
            return content.replace(".", " :)")
        elif style == "poetic":
            return f"V poezii slov: {content}"

        return content

    async def _create_trace_variant(
        self,
        trace: BehavioralTrace,
        affective_profile: AffectiveProfile,
        variant_index: int
    ) -> Optional[Dict[str, str]]:
        """Vytvoří variantu tréninkovacího příkladu z trace"""

        # Různé typy variant
        if variant_index == 0:
            # Emoční varianta
            styled_output = await self._apply_emotional_coloring(
                trace.output_response, affective_profile.primary_emotion
            )
        elif variant_index == 1:
            # Stylová varianta
            styled_output = await self.stylize_for_persona(
                trace.output_response, affective_profile
            )
        else:
            return None

        return {
            "instruction": f"Odpověz ve stylu {affective_profile.get_style_signature()}:",
            "input": trace.input_stimulus,
            "output": styled_output,
            "style_signature": trace.style_signature,
            "emotional_context": trace.emotional_state_before.value,
            "variant_type": f"variant_{variant_index}"
        }
