#!/bin/bash

# NESTOR Volume Backup Script
# Zálohuje všechna Podman volumes do tar archivů

set -e

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Kontrola závislostí
check_dependencies() {
    if ! command -v podman &> /dev/null; then
        print_error "Podman není nainstalován"
        exit 1
    fi
    
    if ! command -v tar &> /dev/null; then
        print_error "tar není dostupný"
        exit 1
    fi
}

# Vytvoření backup adresáře
create_backup_dir() {
    local backup_dir="data/backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    BACKUP_PATH="${backup_dir}/backup_${timestamp}"
    
    mkdir -p "$BACKUP_PATH"
    print_info "Vytvořen backup adresář: $BACKUP_PATH"
}

# Získání seznamu volumes
get_volumes() {
    print_info "Získávám seznam NESTOR volumes..."
    
    # Získání volumes z compose souboru
    VOLUMES=(
        "nestor_postgres_data"
        "nestor_redis_data"
    )
    
    # Kontrola existence volumes
    local existing_volumes=()
    for volume in "${VOLUMES[@]}"; do
        if podman volume exists "$volume" 2>/dev/null; then
            existing_volumes+=("$volume")
            print_info "Nalezen volume: $volume"
        else
            print_warning "Volume $volume neexistuje"
        fi
    done
    
    VOLUMES=("${existing_volumes[@]}")
    
    if [ ${#VOLUMES[@]} -eq 0 ]; then
        print_error "Nebyly nalezeny žádné NESTOR volumes"
        exit 1
    fi
}

# Zastavení služeb
stop_services() {
    print_header "ZASTAVOVÁNÍ SLUŽEB"
    
    if [ -f "podman-compose.yml" ]; then
        print_info "Zastavuji NESTOR služby..."
        podman-compose -f podman-compose.yml down
        print_success "Služby zastaveny"
    else
        print_warning "podman-compose.yml nenalezen, přeskakuji zastavení služeb"
    fi
}

# Záloha volume
backup_volume() {
    local volume_name="$1"
    local backup_file="${BACKUP_PATH}/${volume_name}.tar.gz"
    
    print_info "Zálohuji volume: $volume_name"
    
    # Vytvoření dočasného kontejneru pro přístup k volume
    local temp_container="backup_temp_$(date +%s)"
    
    # Spuštění kontejneru s připojeným volume
    podman run --rm \
        --name "$temp_container" \
        -v "${volume_name}:/backup_source:ro" \
        -v "$(pwd)/${BACKUP_PATH}:/backup_dest" \
        docker.io/library/alpine:latest \
        tar -czf "/backup_dest/$(basename "$backup_file")" -C /backup_source .
    
    if [ -f "$backup_file" ]; then
        local size=$(du -h "$backup_file" | cut -f1)
        print_success "Volume $volume_name zálohován ($size): $backup_file"
    else
        print_error "Záloha volume $volume_name selhala"
        return 1
    fi
}

# Záloha databáze (SQL dump)
backup_database() {
    print_header "ZÁLOHA DATABÁZE"
    
    # Spuštění pouze PostgreSQL pro dump
    print_info "Spouštím PostgreSQL pro SQL dump..."
    podman run -d \
        --name nestor-postgres-backup \
        -e POSTGRES_DB=nestor \
        -e POSTGRES_USER=nestor \
        -e POSTGRES_PASSWORD=nestor_password \
        -v nestor_postgres_data:/var/lib/postgresql/data \
        docker.io/pgvector/pgvector:pg15
    
    # Čekání na spuštění
    print_info "Čekám na spuštění PostgreSQL..."
    sleep 10
    
    # Kontrola dostupnosti
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if podman exec nestor-postgres-backup pg_isready -U nestor -d nestor &> /dev/null; then
            print_success "PostgreSQL je dostupný"
            break
        fi
        
        print_info "Pokus $attempt/$max_attempts - čekám na PostgreSQL..."
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "PostgreSQL není dostupný po $max_attempts pokusech"
        podman rm -f nestor-postgres-backup
        return 1
    fi
    
    # SQL dump
    local sql_backup="${BACKUP_PATH}/database_dump.sql"
    print_info "Vytvářím SQL dump..."
    
    podman exec nestor-postgres-backup pg_dump -U nestor -d nestor > "$sql_backup"
    
    if [ -f "$sql_backup" ]; then
        local size=$(du -h "$sql_backup" | cut -f1)
        print_success "SQL dump vytvořen ($size): $sql_backup"
    else
        print_error "Vytvoření SQL dump selhalo"
    fi
    
    # Zastavení a odstranění dočasného kontejneru
    podman rm -f nestor-postgres-backup
}

# Vytvoření metadata souboru
create_metadata() {
    local metadata_file="${BACKUP_PATH}/backup_metadata.txt"
    
    print_info "Vytvářím metadata soubor..."
    
    cat > "$metadata_file" << EOF
NESTOR Backup Metadata
======================

Datum zálohy: $(date)
Hostname: $(hostname)
Uživatel: $(whoami)
Podman verze: $(podman --version)

Zálohované volumes:
EOF
    
    for volume in "${VOLUMES[@]}"; do
        echo "- $volume" >> "$metadata_file"
    done
    
    echo "" >> "$metadata_file"
    echo "Soubory v záloze:" >> "$metadata_file"
    ls -la "$BACKUP_PATH" >> "$metadata_file"
    
    print_success "Metadata soubor vytvořen: $metadata_file"
}

# Spuštění služeb zpět
start_services() {
    print_header "SPOUŠTĚNÍ SLUŽEB"
    
    if [ -f "podman-compose.yml" ]; then
        print_info "Spouštím NESTOR služby..."
        podman-compose -f podman-compose.yml up -d
        print_success "Služby spuštěny"
    else
        print_warning "podman-compose.yml nenalezen, přeskakuji spuštění služeb"
    fi
}

# Hlavní funkce
main() {
    print_header "NESTOR VOLUME BACKUP"
    
    # Kontrola, že jsme v root adresáři projektu
    if [ ! -f "podman-compose.yml" ]; then
        print_error "Tento skript musí být spuštěn z root adresáře NESTOR projektu"
        exit 1
    fi
    
    check_dependencies
    create_backup_dir
    get_volumes
    stop_services
    
    # Záloha každého volume
    print_header "ZÁLOHA VOLUMES"
    for volume in "${VOLUMES[@]}"; do
        backup_volume "$volume"
    done
    
    backup_database
    create_metadata
    start_services
    
    print_header "ZÁLOHA DOKONČENA"
    print_success "Všechny zálohy byly uloženy do: $BACKUP_PATH"
    
    echo ""
    echo "Obsah zálohy:"
    ls -la "$BACKUP_PATH"
    
    echo ""
    echo "Pro obnovení použijte:"
    echo "./scripts/restore_volumes.sh $BACKUP_PATH"
}

# Spuštění hlavní funkce
main "$@"
