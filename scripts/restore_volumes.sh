#!/bin/bash

# NESTOR Volume Restore Script
# O<PERSON><PERSON><PERSON><PERSON> volumes z tar archivů

set -e

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Kontrola argumentů
check_arguments() {
    if [ $# -eq 0 ]; then
        print_error "Použití: $0 <cesta_k_backup_adresáři>"
        echo ""
        echo "Příklady:"
        echo "  $0 data/backups/backup_20231201_143022"
        echo "  $0 /path/to/backup"
        echo ""
        echo "Dostupné zálohy:"
        if [ -d "data/backups" ]; then
            ls -la data/backups/ | grep "backup_"
        else
            echo "  Žádné zálohy nenalezeny"
        fi
        exit 1
    fi
    
    BACKUP_PATH="$1"
    
    if [ ! -d "$BACKUP_PATH" ]; then
        print_error "Backup adresář neexistuje: $BACKUP_PATH"
        exit 1
    fi
    
    print_info "Používám backup z: $BACKUP_PATH"
}

# Kontrola závislostí
check_dependencies() {
    if ! command -v podman &> /dev/null; then
        print_error "Podman není nainstalován"
        exit 1
    fi
    
    if ! command -v tar &> /dev/null; then
        print_error "tar není dostupný"
        exit 1
    fi
}

# Kontrola backup souborů
check_backup_files() {
    print_info "Kontroluji backup soubory..."
    
    local required_files=(
        "nestor_postgres_data.tar.gz"
        "nestor_redis_data.tar.gz"
        "database_dump.sql"
        "backup_metadata.txt"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "${BACKUP_PATH}/${file}" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_warning "Chybějící backup soubory: ${missing_files[*]}"
        print_info "Pokračuji s dostupnými soubory..."
    else
        print_success "Všechny backup soubory nalezeny"
    fi
    
    # Zobrazení metadata
    if [ -f "${BACKUP_PATH}/backup_metadata.txt" ]; then
        print_info "Metadata zálohy:"
        cat "${BACKUP_PATH}/backup_metadata.txt"
        echo ""
    fi
}

# Zastavení služeb
stop_services() {
    print_header "ZASTAVOVÁNÍ SLUŽEB"
    
    if [ -f "podman-compose.yml" ]; then
        print_info "Zastavuji NESTOR služby..."
        podman-compose -f podman-compose.yml down
        print_success "Služby zastaveny"
    else
        print_warning "podman-compose.yml nenalezen, přeskakuji zastavení služeb"
    fi
}

# Odstranění existujících volumes
remove_existing_volumes() {
    print_header "ODSTRANĚNÍ EXISTUJÍCÍCH VOLUMES"
    
    local volumes=(
        "nestor_postgres_data"
        "nestor_redis_data"
    )
    
    for volume in "${volumes[@]}"; do
        if podman volume exists "$volume" 2>/dev/null; then
            print_warning "Odstraňuji existující volume: $volume"
            podman volume rm "$volume"
            print_info "Volume $volume odstraněn"
        else
            print_info "Volume $volume neexistuje"
        fi
    done
}

# Obnovení volume
restore_volume() {
    local volume_name="$1"
    local backup_file="${BACKUP_PATH}/${volume_name}.tar.gz"
    
    if [ ! -f "$backup_file" ]; then
        print_warning "Backup soubor nenalezen: $backup_file"
        return 1
    fi
    
    print_info "Obnovuji volume: $volume_name"
    
    # Vytvoření nového volume
    podman volume create "$volume_name"
    
    # Vytvoření dočasného kontejneru pro obnovení
    local temp_container="restore_temp_$(date +%s)"
    
    # Obnovení dat z tar archivu
    podman run --rm \
        --name "$temp_container" \
        -v "${volume_name}:/restore_dest" \
        -v "$(pwd)/${BACKUP_PATH}:/backup_source:ro" \
        docker.io/library/alpine:latest \
        tar -xzf "/backup_source/$(basename "$backup_file")" -C /restore_dest
    
    print_success "Volume $volume_name obnoven z $backup_file"
}

# Obnovení databáze z SQL dump
restore_database() {
    print_header "OBNOVENÍ DATABÁZE"
    
    local sql_backup="${BACKUP_PATH}/database_dump.sql"
    
    if [ ! -f "$sql_backup" ]; then
        print_warning "SQL dump nenalezen: $sql_backup"
        print_info "Přeskakuji obnovení databáze"
        return 1
    fi
    
    # Spuštění PostgreSQL pro obnovení
    print_info "Spouštím PostgreSQL pro obnovení databáze..."
    podman run -d \
        --name nestor-postgres-restore \
        -e POSTGRES_DB=nestor \
        -e POSTGRES_USER=nestor \
        -e POSTGRES_PASSWORD=nestor_password \
        -v nestor_postgres_data:/var/lib/postgresql/data \
        docker.io/pgvector/pgvector:pg15
    
    # Čekání na spuštění
    print_info "Čekám na spuštění PostgreSQL..."
    sleep 10
    
    # Kontrola dostupnosti
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if podman exec nestor-postgres-restore pg_isready -U nestor -d nestor &> /dev/null; then
            print_success "PostgreSQL je dostupný"
            break
        fi
        
        print_info "Pokus $attempt/$max_attempts - čekám na PostgreSQL..."
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "PostgreSQL není dostupný po $max_attempts pokusech"
        podman rm -f nestor-postgres-restore
        return 1
    fi
    
    # Obnovení z SQL dump
    print_info "Obnovuji databázi z SQL dump..."
    
    # Nejprve vyčistíme databázi
    podman exec nestor-postgres-restore psql -U nestor -d nestor -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
    
    # Obnovíme z dump
    podman exec -i nestor-postgres-restore psql -U nestor -d nestor < "$sql_backup"
    
    print_success "Databáze obnovena z SQL dump"
    
    # Zastavení a odstranění dočasného kontejneru
    podman rm -f nestor-postgres-restore
}

# Spuštění služeb
start_services() {
    print_header "SPOUŠTĚNÍ SLUŽEB"
    
    if [ -f "podman-compose.yml" ]; then
        print_info "Spouštím NESTOR služby..."
        podman-compose -f podman-compose.yml up -d
        
        print_info "Čekám na spuštění služeb..."
        sleep 15
        
        print_success "Služby spuštěny"
    else
        print_warning "podman-compose.yml nenalezen, přeskakuji spuštění služeb"
    fi
}

# Ověření obnovení
verify_restore() {
    print_header "OVĚŘENÍ OBNOVENÍ"
    
    # Kontrola volumes
    local volumes=(
        "nestor_postgres_data"
        "nestor_redis_data"
    )
    
    for volume in "${volumes[@]}"; do
        if podman volume exists "$volume" 2>/dev/null; then
            print_success "Volume $volume existuje"
        else
            print_error "Volume $volume neexistuje"
        fi
    done
    
    # Kontrola služeb
    print_info "Kontroluji stav služeb..."
    if [ -f "podman-compose.yml" ]; then
        podman-compose -f podman-compose.yml ps
    fi
}

# Hlavní funkce
main() {
    print_header "NESTOR VOLUME RESTORE"
    
    # Kontrola, že jsme v root adresáři projektu
    if [ ! -f "podman-compose.yml" ]; then
        print_error "Tento skript musí být spuštěn z root adresáře NESTOR projektu"
        exit 1
    fi
    
    check_arguments "$@"
    check_dependencies
    check_backup_files
    
    # Potvrzení od uživatele
    echo ""
    print_warning "VAROVÁNÍ: Tato operace odstraní všechna současná data!"
    read -p "Pokračovat s obnovením? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Obnovení zrušeno uživatelem"
        exit 0
    fi
    
    stop_services
    remove_existing_volumes
    
    # Obnovení volumes
    print_header "OBNOVENÍ VOLUMES"
    restore_volume "nestor_postgres_data"
    restore_volume "nestor_redis_data"
    
    restore_database
    start_services
    verify_restore
    
    print_header "OBNOVENÍ DOKONČENO"
    print_success "Všechna data byla obnovena z: $BACKUP_PATH"
    
    echo ""
    echo "Služby jsou dostupné na:"
    echo "• Main API:          http://localhost:8000"
    echo "• Memory Service:    http://localhost:8001"
    echo "• RAG Service:       http://localhost:8002"
    echo "• LLM Interface:     http://localhost:8003"
    echo "• Tokenization:      http://localhost:8004"
}

# Spuštění hlavní funkce
main "$@"
