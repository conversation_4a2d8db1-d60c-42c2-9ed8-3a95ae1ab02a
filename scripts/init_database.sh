#!/bin/bash

# NESTOR Database Initialization Script
# Inicializuje datab<PERSON>zi s migracemi a seed daty

set -e

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Kontrola závislostí
check_dependencies() {
    if ! command -v podman &> /dev/null; then
        print_error "Podman není nainstalován"
        exit 1
    fi
}

# Kontrola běžícího PostgreSQL kontejneru
check_postgres() {
    print_info "Kontroluji PostgreSQL kontejner..."
    
    if ! podman ps | grep -q "nestor-postgres"; then
        print_error "PostgreSQL kontejner neběží"
        print_info "Spusťte nejprve: podman-compose -f podman-compose.yml up -d postgres"
        exit 1
    fi
    
    # Čekání na dostupnost
    local max_attempts=30
    local attempt=1
    
    print_info "Čekám na dostupnost PostgreSQL..."
    
    while [ $attempt -le $max_attempts ]; do
        if podman exec nestor-postgres pg_isready -U nestor -d nestor &> /dev/null; then
            print_success "PostgreSQL je dostupný"
            break
        fi
        
        print_info "Pokus $attempt/$max_attempts - čekám na PostgreSQL..."
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "PostgreSQL není dostupný po $max_attempts pokusech"
        exit 1
    fi
}

# Spuštění migrací
run_migrations() {
    print_header "SPOUŠTĚNÍ MIGRACÍ"
    
    local migration_files=(
        "data/migrations/01_init.sql"
    )
    
    for migration in "${migration_files[@]}"; do
        if [ -f "$migration" ]; then
            print_info "Spouštím migraci: $migration"
            
            if podman exec -i nestor-postgres psql -U nestor -d nestor < "$migration"; then
                print_success "Migrace $migration dokončena"
            else
                print_error "Migrace $migration selhala"
                exit 1
            fi
        else
            print_warning "Migrace $migration nenalezena"
        fi
    done
}

# Načtení seed dat
load_seed_data() {
    print_header "NAČÍTÁNÍ SEED DAT"
    
    local seed_files=(
        "data/seeds/sample_data.sql"
    )
    
    for seed in "${seed_files[@]}"; do
        if [ -f "$seed" ]; then
            print_info "Načítám seed data: $seed"
            
            if podman exec -i nestor-postgres psql -U nestor -d nestor < "$seed"; then
                print_success "Seed data $seed načtena"
            else
                print_warning "Načtení seed dat $seed selhalo (možná již existují)"
            fi
        else
            print_info "Seed soubor $seed nenalezen, přeskakuji"
        fi
    done
}

# Ověření databáze
verify_database() {
    print_header "OVĚŘENÍ DATABÁZE"
    
    print_info "Kontroluji tabulky..."
    
    # Kontrola existence tabulek
    local tables=(
        "users"
        "personalities" 
        "memories"
        "memory_embeddings"
        "documents"
        "document_chunks"
        "chunk_embeddings"
        "conversations"
        "conversation_messages"
    )
    
    for table in "${tables[@]}"; do
        local count=$(podman exec nestor-postgres psql -U nestor -d nestor -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name='$table';" | tr -d ' ')
        
        if [ "$count" = "1" ]; then
            print_success "Tabulka $table existuje"
        else
            print_error "Tabulka $table neexistuje"
        fi
    done
    
    # Kontrola dat
    print_info "Kontroluji data..."
    
    local user_count=$(podman exec nestor-postgres psql -U nestor -d nestor -t -c "SELECT COUNT(*) FROM users;" | tr -d ' ')
    local personality_count=$(podman exec nestor-postgres psql -U nestor -d nestor -t -c "SELECT COUNT(*) FROM personalities;" | tr -d ' ')
    local memory_count=$(podman exec nestor-postgres psql -U nestor -d nestor -t -c "SELECT COUNT(*) FROM memories;" | tr -d ' ')
    
    print_info "Počet uživatelů: $user_count"
    print_info "Počet osobností: $personality_count"
    print_info "Počet pamětí: $memory_count"
    
    if [ "$user_count" -gt 0 ] && [ "$personality_count" -gt 0 ]; then
        print_success "Databáze obsahuje základní data"
    else
        print_warning "Databáze neobsahuje očekávaná data"
    fi
}

# Reset databáze (volitelné)
reset_database() {
    print_header "RESET DATABÁZE"
    
    print_warning "VAROVÁNÍ: Tato operace odstraní všechna data!"
    read -p "Pokračovat s resetem databáze? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Reset zrušen uživatelem"
        return 0
    fi
    
    print_info "Odstraňuji všechna data..."
    
    # Odstranění všech tabulek
    podman exec nestor-postgres psql -U nestor -d nestor -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
    
    print_success "Databáze byla resetována"
}

# Zobrazení nápovědy
show_help() {
    echo "NESTOR Database Initialization Script"
    echo ""
    echo "Použití: $0 [OPTION]"
    echo ""
    echo "Možnosti:"
    echo "  --init          Spustí migrace a načte seed data (výchozí)"
    echo "  --migrate-only  Spustí pouze migrace"
    echo "  --seed-only     Načte pouze seed data"
    echo "  --reset         Resetuje databázi a spustí init"
    echo "  --verify        Pouze ověří stav databáze"
    echo "  --help          Zobrazí tuto nápovědu"
    echo ""
    echo "Příklady:"
    echo "  $0                    # Kompletní inicializace"
    echo "  $0 --migrate-only     # Pouze migrace"
    echo "  $0 --seed-only        # Pouze seed data"
    echo "  $0 --reset            # Reset a reinicializace"
    echo "  $0 --verify           # Kontrola stavu"
}

# Hlavní funkce
main() {
    local action="init"
    
    # Zpracování argumentů
    case "${1:-}" in
        --init)
            action="init"
            ;;
        --migrate-only)
            action="migrate"
            ;;
        --seed-only)
            action="seed"
            ;;
        --reset)
            action="reset"
            ;;
        --verify)
            action="verify"
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            action="init"
            ;;
        *)
            print_error "Neznámá možnost: $1"
            show_help
            exit 1
            ;;
    esac
    
    print_header "NESTOR DATABASE INITIALIZATION"
    
    # Kontrola, že jsme v root adresáři projektu
    if [ ! -f "podman-compose.yml" ]; then
        print_error "Tento skript musí být spuštěn z root adresáře NESTOR projektu"
        exit 1
    fi
    
    check_dependencies
    check_postgres
    
    case $action in
        "init")
            run_migrations
            load_seed_data
            verify_database
            ;;
        "migrate")
            run_migrations
            verify_database
            ;;
        "seed")
            load_seed_data
            verify_database
            ;;
        "reset")
            reset_database
            run_migrations
            load_seed_data
            verify_database
            ;;
        "verify")
            verify_database
            ;;
    esac
    
    print_success "Operace dokončena úspěšně!"
}

# Spuštění hlavní funkce
main "$@"
