# NESTOR Scripts

Tento adres<PERSON>ř obsahuje pomocné skripty pro správu NESTOR vývojového prostředí.

## 📁 P<PERSON>ehled skriptů

### 🚀 setup_environment.sh (hlavn<PERSON> skript)
**Umístění:** `./setup_environment.sh` (v root adresáři)

Hlavní skript pro automatickou přípravu celého vývojového prostředí.

```bash
./setup_environment.sh
```

**Co dělá:**
- Detekuje Linux distribuci
- Instaluje Podman a podman-compose
- Kontroluje závislosti
- Vytváří .env soubor
- Spouští všechny služby
- Inicializuje databázi
- Načítá ukázková data

### 💾 backup_volumes.sh
Vytváří zálohu všech Podman volumes a databáze.

```bash
./scripts/backup_volumes.sh
```

**Výstup:**
- `data/backups/backup_YYYYMMDD_HHMMSS/`
  - `nestor_postgres_data.tar.gz`
  - `nestor_redis_data.tar.gz`
  - `database_dump.sql`
  - `backup_metadata.txt`

### 🔄 restore_volumes.sh
Obnovuje data z dříve vytvořené zálohy.

```bash
./scripts/restore_volumes.sh data/backups/backup_20231201_143022
```

**⚠️ VAROVÁNÍ:** Odstraní všechna současná data!

### 🗄️ init_database.sh
Inicializuje databázi s migracemi a seed daty.

```bash
# Kompletní inicializace
./scripts/init_database.sh

# Pouze migrace
./scripts/init_database.sh --migrate-only

# Pouze seed data
./scripts/init_database.sh --seed-only

# Reset databáze
./scripts/init_database.sh --reset

# Kontrola stavu
./scripts/init_database.sh --verify
```

## 🔧 Typické použití

### První spuštění po klonování
```bash
# 1. Nastavení oprávnění
chmod +x setup_environment.sh scripts/*.sh

# 2. Spuštění setup
./setup_environment.sh
```

### Pravidelná záloha
```bash
# Vytvoření zálohy
./scripts/backup_volumes.sh

# Zkopírování na externí úložiště
cp -r data/backups/backup_YYYYMMDD_HHMMSS /path/to/external/storage/
```

### Obnova po přeinstalaci PC
```bash
# 1. Klonování projektu
git clone https://github.com/your-username/nestor.git
cd nestor

# 2. Setup prostředí
./setup_environment.sh

# 3. Obnovení dat (volitelné)
./scripts/restore_volumes.sh data/backups/backup_YYYYMMDD_HHMMSS
```

### Reset vývojového prostředí
```bash
# Zastavení služeb
podman-compose -f podman-compose.yml down

# Reset databáze
./scripts/init_database.sh --reset

# Spuštění služeb
podman-compose -f podman-compose.yml up -d
```

## 🛠️ Řešení problémů

### Skript se nespustí
```bash
# Kontrola oprávnění
ls -la scripts/
chmod +x scripts/*.sh
```

### PostgreSQL není dostupný
```bash
# Kontrola kontejneru
podman ps | grep postgres

# Restart PostgreSQL
podman-compose restart postgres

# Kontrola logů
podman logs nestor-postgres
```

### Záloha selhala
```bash
# Kontrola místa na disku
df -h

# Kontrola volumes
podman volume ls

# Vyčištění starých dat
podman system prune
```

### Obnovení selhalo
```bash
# Kontrola backup souborů
ls -la data/backups/backup_YYYYMMDD_HHMMSS/

# Manuální obnovení databáze
podman exec -i nestor-postgres psql -U nestor -d nestor < backup_path/database_dump.sql
```

## 📋 Požadavky

### Systémové požadavky
- Linux (Fedora, Ubuntu, Debian, Arch, openSUSE)
- Bash shell
- sudo oprávnění (pro instalaci balíčků)

### Automaticky instalované závislosti
- Podman
- podman-compose
- curl
- git
- tar

## 🔍 Logování

Všechny skripty používají barevný výstup:
- 🔵 **[INFO]** - Informační zprávy
- 🟢 **[SUCCESS]** - Úspěšné operace
- 🟡 **[WARNING]** - Varování
- 🔴 **[ERROR]** - Chyby

## 📝 Poznámky

### Bezpečnost
- Skripty používají `set -e` pro ukončení při chybě
- Zálohy obsahují metadata pro ověření integrity
- Obnovení vyžaduje potvrzení uživatele

### Výkon
- Zálohy jsou komprimované (gzip)
- Volumes se zálohují pomocí dočasných kontejnerů
- Databáze se zálohuje jako SQL dump pro přenositelnost

### Kompatibilita
- Skripty jsou testované na Fedora 42
- Podporují hlavní Linux distribuce
- Používají standardní POSIX příkazy
