# NESTOR Project Makefile
# Automatizace pro správu kontejnerů a služeb

.PHONY: help init build up down status logs clean db-up db-migrate db-seed health-check

# Default target
help:
	@echo "NESTOR Project - Makefile Commands"
	@echo "=================================="
	@echo "🚀 Základní příkazy:"
	@echo "init          - Inicializace projektu"
	@echo "build         - Build všech kontejnerů"
	@echo "up            - Spuštění všech služeb"
	@echo "down          - Zastavení všech služeb"
	@echo "status        - Stav všech kontejnerů"
	@echo "restart       - Restart všech služeb"
	@echo "clean         - Vyčištění kontejnerů a volumes"
	@echo ""
	@echo "🗄️ Databáze:"
	@echo "db-up         - Spuštění databáze"
	@echo "db-migrate    - Spuštění migrac<PERSON>"
	@echo "db-seed       - Naplněn<PERSON> testovacími daty"
	@echo ""
	@echo "🏥 Monitoring:"
	@echo "health-check  - Kontrola zdraví služeb"
	@echo "logs          - Zobrazení logů všech služeb"
	@echo "logs-api      - Logy API služby"
	@echo "logs-mcp      - Logy Memory služby"
	@echo "logs-rag      - Logy RAG služby"
	@echo "logs-llm      - Logy LLM služby"
	@echo "logs-tokenization - Logy Tokenization služby"
	@echo ""
	@echo "🧪 Testování:"
	@echo "test          - Spuštění všech testů"
	@echo "test-unit     - Unit testy"
	@echo "test-integration - Integrační testy"
	@echo "test-e2e      - End-to-end testy"
	@echo "test-coverage - Testy s pokrytím"
	@echo ""
	@echo "🔧 Vývoj:"
	@echo "lint          - Kontrola kvality kódu"
	@echo "format        - Formátování kódu"
	@echo "dev-setup     - Nastavení vývojového prostředí"
	@echo ""
	@echo "📊 CLI nástroje:"
	@echo "cli-status    - Stav služeb přes CLI"
	@echo "cli-info      - Informace o platformě"
	@echo ""
	@echo "📚 Dokumentace:"
	@echo "docs-serve    - Spuštění dokumentace"
	@echo "docs-build    - Build dokumentace"
	@echo ""
	@echo "🔧 Git:"
	@echo "git-init      - Inicializace Git repozitáře"

# Inicializace projektu
init:
	@echo "🚀 Inicializace NESTOR projektu..."
	@mkdir -p api memory vectorstore llm_interface tokenization frontend
	@mkdir -p data/migrations data/seeds data/samples
	@mkdir -p docs tests cli k8s monitoring security ci scripts
	@mkdir -p logs
	@cp .env.example .env 2>/dev/null || echo "# NESTOR Environment Variables" > .env
	@echo "✅ Projekt inicializován"

# Build všech kontejnerů
build:
	@echo "🔨 Building kontejnery..."
	podman-compose build
	@echo "✅ Build dokončen"

# Spuštění všech služeb
up:
	@echo "🚀 Spouštění služeb..."
	podman-compose up -d
	@echo "✅ Služby spuštěny"
	@echo "📊 Přístup k službám:"
	@echo "  - API: http://localhost:8000"
	@echo "  - Memory Context Processor: http://localhost:8001"
	@echo "  - RAG Service: http://localhost:8002"
	@echo "  - LLM Service: http://localhost:8003"
	@echo "  - PostgreSQL: localhost:5432"
	@echo "  - Redis: localhost:6379"

# Zastavení všech služeb
down:
	@echo "🛑 Zastavování služeb..."
	podman-compose down
	@echo "✅ Služby zastaveny"

# Stav kontejnerů
status:
	@echo "📊 Stav kontejnerů:"
	podman-compose ps

# Zobrazení logů
logs:
	podman-compose logs -f

# Vyčištění
clean:
	@echo "🧹 Čištění kontejnerů a volumes..."
	podman-compose down -v
	podman system prune -f
	@echo "✅ Vyčištěno"

# Spuštění pouze databáze
db-up:
	@echo "🗄️ Spouštění databáze..."
	podman-compose up -d postgres redis
	@sleep 5
	@echo "✅ Databáze spuštěna"

# Spuštění migrací
db-migrate:
	@echo "🔄 Spouštění migrací..."
	podman-compose exec postgres psql -U nestor -d nestor -c "CREATE EXTENSION IF NOT EXISTS vector;"
	@echo "✅ Migrace dokončeny"

# Naplnění testovacími daty
db-seed:
	@echo "🌱 Naplňování testovacími daty..."
	@echo "✅ Testovací data načtena"

# Kontrola zdraví služeb
health-check:
	@echo "🏥 Kontrola zdraví služeb..."
	@curl -s http://localhost:8000/health || echo "❌ API nedostupné"
	@curl -s http://localhost:8001/health || echo "❌ MCP nedostupné"
	@curl -s http://localhost:8002/health || echo "❌ RAG nedostupné"
	@curl -s http://localhost:8003/health || echo "❌ LLM nedostupné"
	@curl -s http://localhost:8004/health || echo "❌ Tokenization nedostupné"
	@echo "✅ Kontrola dokončena"

# Restart služeb
restart:
	@echo "🔄 Restart služeb..."
	$(MAKE) down
	$(MAKE) up

# Zobrazení logů konkrétní služby
logs-api:
	podman-compose logs -f api

logs-mcp:
	podman-compose logs -f memory

logs-rag:
	podman-compose logs -f vectorstore

logs-llm:
	podman-compose logs -f llm_interface

logs-tokenization:
	podman-compose logs -f tokenization

logs-db:
	podman-compose logs -f postgres

# Development příkazy
dev-setup:
	@echo "🛠️ Nastavení vývojového prostředí..."
	python -m venv venv
	source venv/bin/activate && pip install -r requirements.txt
	@echo "✅ Vývojové prostředí připraveno"

test:
	@echo "🧪 Spouštění testů..."
	pytest tests/
	@echo "✅ Testy dokončeny"

# Monitoring
monitoring-up:
	@echo "📊 Spouštění monitoring stacku..."
	podman-compose -f monitoring/docker-compose.monitoring.yml up -d
	@echo "✅ Monitoring spuštěn"
	@echo "📊 Grafana: http://localhost:3000"
	@echo "📊 Prometheus: http://localhost:9090"

monitoring-down:
	podman-compose -f monitoring/docker-compose.monitoring.yml down

# Linting a formátování
lint:
	@echo "🔍 Kontrola kvality kódu..."
	black --check .
	isort --check-only .
	flake8 .

format:
	@echo "🎨 Formátování kódu..."
	black .
	isort .

# CLI nástroje
cli-status:
	@echo "📊 Stav služeb přes CLI..."
	python -m cli.main status

cli-info:
	@echo "ℹ️ Informace o platformě..."
	python -m cli.main info

# Git operace
git-init:
	@echo "🔧 Inicializace Git repozitáře..."
	git init
	git add .
	git commit -m "Initial commit: NESTOR platform setup"
	@echo "✅ Git repozitář inicializován s prvním commitem"

# Testování
test-unit:
	@echo "🧪 Spouštění unit testů..."
	pytest tests/unit/ -v

test-integration:
	@echo "🧪 Spouštění integračních testů..."
	pytest tests/integration/ -v

test-e2e:
	@echo "🧪 Spouštění E2E testů..."
	pytest tests/e2e/ -v

test-coverage:
	@echo "🧪 Spouštění testů s pokrytím..."
	pytest tests/ --cov=. --cov-report=html

# Dokumentace
docs-serve:
	@echo "📚 Spouštění dokumentace..."
	mkdocs serve

docs-build:
	@echo "📚 Buildování dokumentace..."
	mkdocs build
