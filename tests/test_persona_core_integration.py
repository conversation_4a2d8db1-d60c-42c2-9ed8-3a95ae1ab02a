"""
Test integrace persona_core modulu s LLM Interface Service

Tento test ově<PERSON><PERSON><PERSON> správnou integraci všech komponent persona_core
s LLM Interface Service a Memory Context Processor.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any

from persona_core.affective_profile import (
    AffectiveProfile, EmotionalState, BehavioralTrait, AffectiveState
)
from persona_core.reconstruction_engine import (
    ReconstructionEngine, ReconstructionContext, ReconstructionResult
)
from persona_core.trace_replayer import TraceReplayer, BehavioralTrace, TraceType
from persona_core.imprint_manager import ImprintManager, PersonaImprint
from persona_core.dream_prompter import DreamPrompter, DreamContext
from persona_core.dataset_builder import PersonaDatasetBuilder

from llm_interface.services.generation_service import GenerationService
from memory.core.context_processor import ContextProcessor


class TestPersonaCoreIntegration:
    """Test suite pro integraci persona_core modulu"""
    
    @pytest.fixture
    async def affective_profile(self):
        """Vyt<PERSON><PERSON>í testovací affective profile"""
        return AffectiveProfile(
            persona_id="test_persona_001",
            name="Test Persona",
            primary_emotion=EmotionalState.HAPPY,
            primary_traits=[BehavioralTrait.CREATIVE, BehavioralTrait.EMPATHETIC],
            communication_style="warm and engaging",
            linguistic_markers=["často používá metafory", "pozitivní jazyk"],
            current_affective_state=AffectiveState(
                intensity=0.7,
                valence=0.8,
                arousal=0.6,
                dominance=0.5
            )
        )
    
    @pytest.fixture
    async def generation_service(self):
        """Vytvoří testovací generation service"""
        service = GenerationService(model_service=None)
        await service.initialize()
        return service
    
    @pytest.fixture
    async def context_processor(self):
        """Vytvoří testovací context processor"""
        return ContextProcessor(memory_manager=None, trace_replayer=None)
    
    @pytest.mark.asyncio
    async def test_affective_profile_creation(self, affective_profile):
        """Test vytvoření affective profile"""
        assert affective_profile.persona_id == "test_persona_001"
        assert affective_profile.name == "Test Persona"
        assert affective_profile.primary_emotion == EmotionalState.HAPPY
        assert BehavioralTrait.CREATIVE in affective_profile.primary_traits
        
        # Test style signature
        style_signature = affective_profile.get_style_signature()
        assert isinstance(style_signature, str)
        assert len(style_signature) > 0
    
    @pytest.mark.asyncio
    async def test_reconstruction_engine_basic(self, affective_profile):
        """Test základní funkcionality reconstruction engine"""
        engine = ReconstructionEngine(memory_manager=None, llm_service=None)
        
        context = ReconstructionContext(
            persona_id="test_persona_001",
            input_text="Jak se máš?",
            conversation_history=[],
            current_emotion=EmotionalState.HAPPY
        )
        
        # Test mock rekonstrukce
        result = await engine.reconstruct_response(
            context=context,
            affective_profile=affective_profile,
            imprint_data=None
        )
        
        assert isinstance(result, ReconstructionResult)
        assert result.reconstructed_text is not None
        assert result.confidence_score >= 0.0
        assert result.confidence_score <= 1.0
        assert result.emotional_coloring == EmotionalState.HAPPY
    
    @pytest.mark.asyncio
    async def test_trace_replayer_recording(self, affective_profile):
        """Test záznamu behaviorální stopy"""
        replayer = TraceReplayer()
        
        # Záznam stopy
        await replayer.record_trace(
            persona_id="test_persona_001",
            trace_type=TraceType.CONVERSATION,
            input_stimulus="Ahoj, jak se máš?",
            output_response="Ahoj! Mám se skvěle, děkuji za optání!",
            affective_profile=affective_profile,
            context={"session_id": "test_session"}
        )
        
        # Ověření záznamu
        traces = await replayer.get_traces("test_persona_001")
        assert len(traces) == 1
        
        trace = traces[0]
        assert trace.persona_id == "test_persona_001"
        assert trace.trace_type == TraceType.CONVERSATION
        assert trace.input_stimulus == "Ahoj, jak se máš?"
    
    @pytest.mark.asyncio
    async def test_imprint_manager_creation(self):
        """Test vytvoření a správy imprintů"""
        manager = ImprintManager(storage_backend=None, training_backend=None)
        
        # Vytvoření imprintu
        imprint = await manager.create_imprint(
            persona_id="test_persona_001",
            name="Test Imprint",
            description="Testovací imprint pro osobnost",
            base_model="test_model",
            training_data_path="/tmp/test_data"
        )
        
        assert isinstance(imprint, PersonaImprint)
        assert imprint.persona_id == "test_persona_001"
        assert imprint.name == "Test Imprint"
        assert imprint.status == "created"
    
    @pytest.mark.asyncio
    async def test_dream_prompter_transformation(self, affective_profile):
        """Test symbolické transformace dream prompter"""
        prompter = DreamPrompter()
        
        context = DreamContext(
            persona_id="test_persona_001",
            original_prompt="Popište krásný den",
            emotional_state=EmotionalState.HAPPY,
            style_preferences=["poetický", "metaforický"]
        )
        
        # Transformace promptu
        result = await prompter.transform_prompt(context, affective_profile)
        
        assert result.transformed_prompt is not None
        assert result.transformation_type is not None
        assert len(result.applied_techniques) > 0
    
    @pytest.mark.asyncio
    async def test_dataset_builder_conversion(self, affective_profile):
        """Test převodu vzpomínek na tréninková data"""
        builder = PersonaDatasetBuilder()
        
        memories = [
            {
                "content": "Pamatuji si krásný den na pláži",
                "emotion": "happy",
                "context": "vacation"
            },
            {
                "content": "Byl jsem smutný, když jsem se loučil",
                "emotion": "sad", 
                "context": "farewell"
            }
        ]
        
        # Konverze na dataset
        dataset = await builder.build_conversation_dataset(
            persona_id="test_persona_001",
            memories=memories,
            affective_profile=affective_profile
        )
        
        assert len(dataset) > 0
        assert all("input" in item and "output" in item for item in dataset)
    
    @pytest.mark.asyncio
    async def test_generation_service_persona_integration(
        self, 
        generation_service, 
        affective_profile
    ):
        """Test integrace generation service s persona_core"""
        
        # Test generování s persona podporou
        result = await generation_service.generate_with_persona(
            prompt="Jak se máš?",
            persona_id="test_persona_001",
            affective_profile=affective_profile,
            conversation_history=[],
            model="mock",
            max_tokens=100
        )
        
        assert "text" in result
        assert "persona_id" in result
        assert result["persona_id"] == "test_persona_001"
        assert "style_signature" in result
        assert "emotional_coloring" in result
        assert result["emotional_coloring"] == EmotionalState.HAPPY.value
        assert result["metadata"]["persona_enhanced"] is True
    
    @pytest.mark.asyncio
    async def test_context_processor_persona_support(
        self, 
        context_processor, 
        affective_profile
    ):
        """Test context processor s persona podporou"""
        
        # Inicializace kontextu
        context = await context_processor.initialize_context(
            persona_id="test_persona_001",
            session_id="test_session",
            affective_profile=affective_profile
        )
        
        assert context.persona_id == "test_persona_001"
        assert context.current_emotional_state == EmotionalState.HAPPY
        assert context.style_signature == affective_profile.get_style_signature()
        assert context.last_style_layer is not None
        
        # Aktualizace kontextu
        updated_context = await context_processor.update_context(
            persona_id="test_persona_001",
            session_id="test_session",
            user_input="Ahoj!",
            assistant_response="Ahoj! Jak se máš?",
            affective_profile=affective_profile
        )
        
        assert updated_context.interaction_count == 1
        assert len(updated_context.current_conversation) == 2
    
    @pytest.mark.asyncio
    async def test_full_persona_pipeline(
        self, 
        generation_service, 
        context_processor, 
        affective_profile
    ):
        """Test kompletního persona pipeline"""
        
        # 1. Inicializace kontextu
        context = await context_processor.initialize_context(
            persona_id="test_persona_001",
            session_id="test_session",
            affective_profile=affective_profile
        )
        
        # 2. Generování odpovědi s persona podporou
        result = await generation_service.generate_with_persona(
            prompt="Řekni mi něco o sobě",
            persona_id="test_persona_001",
            affective_profile=affective_profile,
            conversation_history=context.current_conversation,
            model="mock"
        )
        
        # 3. Aktualizace kontextu
        updated_context = await context_processor.update_context(
            persona_id="test_persona_001",
            session_id="test_session",
            user_input="Řekni mi něco o sobě",
            assistant_response=result["text"],
            affective_profile=affective_profile
        )
        
        # Ověření výsledků
        assert result["persona_enhanced"] is True
        assert updated_context.interaction_count == 1
        assert updated_context.current_emotional_state == EmotionalState.HAPPY
        
        # 4. Získání shrnutí kontextu
        summary = await context_processor.get_context_summary(
            persona_id="test_persona_001",
            session_id="test_session"
        )
        
        assert summary["persona_id"] == "test_persona_001"
        assert summary["interaction_count"] == 1
        assert summary["current_emotional_state"] == EmotionalState.HAPPY.value
    
    @pytest.mark.asyncio
    async def test_error_handling(self, generation_service):
        """Test error handling v persona integraci"""
        
        # Test s neplatným persona_id
        result = await generation_service.generate_with_persona(
            prompt="Test",
            persona_id="invalid_persona",
            affective_profile=None,  # Žádný profile
            model="mock"
        )
        
        # Mělo by fallback na standardní generování
        assert "text" in result
        assert result["persona_id"] == "invalid_persona"
        
    @pytest.mark.asyncio
    async def test_performance_metrics(self, generation_service, affective_profile):
        """Test performance metrik persona systému"""
        
        start_time = datetime.utcnow()
        
        # Vícenásobné generování pro test výkonu
        tasks = []
        for i in range(5):
            task = generation_service.generate_with_persona(
                prompt=f"Test prompt {i}",
                persona_id="test_persona_001",
                affective_profile=affective_profile,
                model="mock"
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        # Ověření výsledků
        assert len(results) == 5
        assert all("persona_enhanced" in result for result in results)
        
        # Performance check - mělo by být rychlé
        assert duration < 5.0  # Méně než 5 sekund pro 5 requestů
        
        print(f"Persona pipeline performance: {duration:.2f}s for 5 requests")
