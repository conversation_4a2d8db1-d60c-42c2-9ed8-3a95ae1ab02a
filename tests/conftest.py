"""
Pytest configuration and fixtures for NESTOR tests
"""

import pytest
import asyncio
from typing import AsyncGenerator
from httpx import Async<PERSON><PERSON>
from fastapi.testclient import TestClient

# Import your FastAPI apps
# from api.main import app as api_app
# from memory.main import app as memory_app
# from vectorstore.main import app as rag_app

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def api_client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async HTTP client for API testing."""
    # Uncomment when API app is properly importable
    # async with Async<PERSON><PERSON>(app=api_app, base_url="http://test") as client:
    #     yield client
    
    # Temporary placeholder
    async with Async<PERSON>lient(base_url="http://test") as client:
        yield client

@pytest.fixture
def test_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User"
    }

@pytest.fixture
def test_personality_data():
    """Sample personality data for testing."""
    return {
        "name": "Test Personality",
        "description": "A test digital personality",
        "personality_metadata": {
            "traits": ["friendly", "helpful"],
            "background": "Test background"
        }
    }

@pytest.fixture
def test_memory_data():
    """Sample memory data for testing."""
    return {
        "content": "This is a test memory",
        "memory_type": "episodic",
        "importance": 0.8,
        "metadata": {
            "source": "test",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    }

# Database fixtures (to be implemented when database is set up)
@pytest.fixture
async def db_session():
    """Create a test database session."""
    # TODO: Implement database session for testing
    pass

@pytest.fixture
async def test_db():
    """Create and clean up test database."""
    # TODO: Implement test database setup/teardown
    pass
