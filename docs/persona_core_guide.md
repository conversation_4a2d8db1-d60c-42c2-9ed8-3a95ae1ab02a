# Persona Core Module - Průvodce

## Přehled

Persona Core je biologicky inspirovaný systém paměti a stylu, který umožňuje vytváření autentických digitálních osobností. Modul kombinuje emoční inteligenci, behaviorální analýzu a stylovou adaptaci pro vytvoření věrohodných AI osobností.

## Architektura

### Klíčové komponenty

```
persona_core/
├── affective_profile.py      # Emoční a behaviorální parametry
├── reconstruction_engine.py  # Rekonstrukce odpovědí
├── trace_replayer.py        # Behaviorální analýza
├── imprint_manager.py       # LoRA adaptéry
├── dream_prompter.py        # Stylizace výstupů
└── dataset_builder.py       # Tréninková data
```

## Affective Profile

Affective Profile definuje emoční a behaviorální charakteristiky osobnosti.

### Základn<PERSON> použit<PERSON>

```python
from persona_core.affective_profile import (
    AffectiveProfile, EmotionalState, BehavioralTrait, AffectiveState
)

# Vytvoření profilu
profile = AffectiveProfile(
    persona_id="persona_001",
    name="Anna Krásná",
    primary_emotion=EmotionalState.HAPPY,
    primary_traits=[BehavioralTrait.CREATIVE, BehavioralTrait.EMPATHETIC],
    communication_style="warm and engaging",
    linguistic_markers=["často používá metafory", "pozitivní jazyk"],
    current_affective_state=AffectiveState(
        intensity=0.7,
        valence=0.8,
        arousal=0.6,
        dominance=0.5
    )
)

# Získání stylové signatury
style = profile.get_style_signature()
print(f"Style signature: {style}")
```

### Emoční stavy

```python
class EmotionalState(Enum):
    HAPPY = "happy"
    SAD = "sad"
    ANGRY = "angry"
    FEARFUL = "fearful"
    SURPRISED = "surprised"
    DISGUSTED = "disgusted"
    NEUTRAL = "neutral"
    EXCITED = "excited"
    CALM = "calm"
    ANXIOUS = "anxious"
```

### Behaviorální rysy

```python
class BehavioralTrait(Enum):
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    EMPATHETIC = "empathetic"
    ASSERTIVE = "assertive"
    INTROVERTED = "introverted"
    EXTROVERTED = "extroverted"
    OPTIMISTIC = "optimistic"
    PESSIMISTIC = "pessimistic"
    SPONTANEOUS = "spontaneous"
    METHODICAL = "methodical"
```

## Reconstruction Engine

Reconstruction Engine rekonstruuje odpovědi na základě imprintů a kontextu.

### Použití

```python
from persona_core.reconstruction_engine import (
    ReconstructionEngine, ReconstructionContext
)

# Inicializace
engine = ReconstructionEngine(
    memory_manager=memory_manager,
    llm_service=llm_service
)

# Vytvoření kontextu
context = ReconstructionContext(
    persona_id="persona_001",
    input_text="Jak se máš?",
    conversation_history=[],
    current_emotion=EmotionalState.HAPPY
)

# Rekonstrukce odpovědi
result = await engine.reconstruct_response(
    context=context,
    affective_profile=profile,
    imprint_data=imprint_data
)

print(f"Odpověď: {result.reconstructed_text}")
print(f"Confidence: {result.confidence_score}")
```

## Trace Replayer

Trace Replayer analyzuje a přehrává behaviorální vzorce.

### Záznam stopy

```python
from persona_core.trace_replayer import TraceReplayer, TraceType

replayer = TraceReplayer()

# Záznam interakce
await replayer.record_trace(
    persona_id="persona_001",
    trace_type=TraceType.CONVERSATION,
    input_stimulus="Ahoj, jak se máš?",
    output_response="Ahoj! Mám se skvěle!",
    affective_profile=profile,
    context={"session_id": "session_123"}
)
```

### Analýza vzorců

```python
# Získání stop
traces = await replayer.get_traces("persona_001")

# Analýza vzorců
patterns = await replayer.analyze_patterns("persona_001")
print(f"Nalezené vzorce: {patterns}")

# Přehrání sekvence
replay_result = await replayer.replay_sequence(
    persona_id="persona_001",
    sequence_length=5
)
```

## Imprint Manager

Imprint Manager spravuje LoRA adaptéry pro stylizaci.

### Vytvoření imprintu

```python
from persona_core.imprint_manager import ImprintManager

manager = ImprintManager(
    storage_backend=storage,
    training_backend=training
)

# Vytvoření nového imprintu
imprint = await manager.create_imprint(
    persona_id="persona_001",
    name="Anna Style v1.0",
    description="Základní stylový imprint pro Annu",
    base_model="llama-3-8b",
    training_data_path="/data/anna_conversations.jsonl"
)
```

### Správa imprintů

```python
# Aktivace imprintu
await manager.activate_imprint(imprint.id)

# Získání aktivního imprintu
active = manager.get_active_imprint("persona_001")

# Aktualizace metrik
await manager.update_imprint_metrics(
    imprint.id,
    success=True,
    response_time=0.5
)
```

## Dream Prompter

Dream Prompter poskytuje symbolické a poetické transformace.

### Transformace promptu

```python
from persona_core.dream_prompter import DreamPrompter, DreamContext

prompter = DreamPrompter()

context = DreamContext(
    persona_id="persona_001",
    original_prompt="Popište krásný den",
    emotional_state=EmotionalState.HAPPY,
    style_preferences=["poetický", "metaforický"]
)

# Transformace
result = await prompter.transform_prompt(context, profile)
print(f"Transformovaný prompt: {result.transformed_prompt}")
```

## Dataset Builder

Dataset Builder převádí vzpomínky na tréninková data.

### Vytvoření datasetu

```python
from persona_core.dataset_builder import PersonaDatasetBuilder

builder = PersonaDatasetBuilder()

memories = [
    {
        "content": "Pamatuji si krásný den na pláži",
        "emotion": "happy",
        "context": "vacation"
    },
    {
        "content": "Byl jsem smutný při loučení",
        "emotion": "sad",
        "context": "farewell"
    }
]

# Vytvoření conversation datasetu
dataset = await builder.build_conversation_dataset(
    persona_id="persona_001",
    memories=memories,
    affective_profile=profile
)

# Export do souboru
await builder.export_dataset(
    dataset,
    format="jsonl",
    output_path="/data/persona_001_training.jsonl"
)
```

## Integrace s LLM Service

### Generování s persona podporou

```python
from llm_interface.services.generation_service import GenerationService

service = GenerationService()
await service.initialize()

# Generování s persona
result = await service.generate_with_persona(
    prompt="Jak se máš?",
    persona_id="persona_001",
    affective_profile=profile,
    conversation_history=[],
    model="llama-3-8b",
    max_tokens=150
)

print(f"Odpověď: {result['text']}")
print(f"Style: {result['style_signature']}")
print(f"Emotion: {result['emotional_coloring']}")
```

## Integrace s Memory Context

### Context Processor s persona podporou

```python
from memory.core.context_processor import ContextProcessor

processor = ContextProcessor()

# Inicializace kontextu
context = await processor.initialize_context(
    persona_id="persona_001",
    session_id="session_123",
    affective_profile=profile
)

# Aktualizace kontextu
updated_context = await processor.update_context(
    persona_id="persona_001",
    session_id="session_123",
    user_input="Ahoj!",
    assistant_response="Ahoj! Jak se máš?",
    affective_profile=profile
)
```

## Best Practices

### 1. Emoční konzistence
- Udržujte konzistentní emoční stav během konverzace
- Používejte postupné přechody mezi emocemi
- Sledujte emoční historii

### 2. Stylová autenticita
- Definujte jasné jazykové markery
- Používejte konzistentní komunikační styl
- Pravidelně aktualizujte imprints

### 3. Performance optimalizace
- Cachujte často používané profily
- Používejte batch processing pro analýzu
- Monitorujte response times

### 4. Kvalita dat
- Validujte vstupní data
- Používejte quality filtering
- Pravidelně auditujte výstupy

## Troubleshooting

### Časté problémy

1. **Nízká confidence score**
   - Zkontrolujte kvalitu imprint dat
   - Ověřte konzistenci affective profile
   - Zvažte retraining imprintu

2. **Pomalé response times**
   - Optimalizujte imprint velikost
   - Používejte caching
   - Zkontrolujte memory usage

3. **Nekonzistentní styl**
   - Ověřte linguistic markers
   - Zkontrolujte training data
   - Aktualizujte affective profile

### Debugging

```python
# Zapnutí debug módu
import logging
logging.getLogger("persona_core").setLevel(logging.DEBUG)

# Monitoring metrik
from persona_core.metrics import PersonaMetrics

metrics = PersonaMetrics()
stats = await metrics.get_persona_stats("persona_001")
print(f"Stats: {stats}")
```

## Příklady použití

### Kompletní persona pipeline

```python
async def create_persona_pipeline():
    # 1. Vytvoření affective profile
    profile = AffectiveProfile(
        persona_id="writer_001",
        name="Spisovatelka Marie",
        primary_emotion=EmotionalState.CALM,
        primary_traits=[BehavioralTrait.CREATIVE, BehavioralTrait.ANALYTICAL],
        communication_style="thoughtful and articulate",
        linguistic_markers=["používá bohaté metafory", "dlouhé věty"]
    )
    
    # 2. Inicializace komponent
    engine = ReconstructionEngine(memory_manager, llm_service)
    replayer = TraceReplayer()
    manager = ImprintManager(storage, training)
    
    # 3. Vytvoření imprintu
    imprint = await manager.create_imprint(
        persona_id="writer_001",
        name="Marie Writing Style",
        base_model="llama-3-8b",
        training_data_path="/data/marie_texts.jsonl"
    )
    
    # 4. Aktivace imprintu
    await manager.activate_imprint(imprint.id)
    
    # 5. Generování odpovědi
    context = ReconstructionContext(
        persona_id="writer_001",
        input_text="Napište krátký příběh o lásce",
        conversation_history=[],
        current_emotion=EmotionalState.CALM
    )
    
    result = await engine.reconstruct_response(
        context=context,
        affective_profile=profile,
        imprint_data=imprint.dict()
    )
    
    return result
```

Tento průvodce poskytuje kompletní přehled persona_core modulu a jeho integrace do NESTOR platformy.
