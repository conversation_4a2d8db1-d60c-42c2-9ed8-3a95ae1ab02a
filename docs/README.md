# NESTOR Documentation

Welcome to the NESTOR platform documentation. This directory contains comprehensive documentation for developers, users, and administrators.

## Documentation Structure

### 📚 [API Documentation](api/)
- OpenAPI specifications
- Endpoint documentation
- Request/response examples
- Authentication guides

### 🏗️ [Architecture](architecture/)
- System overview
- Microservices design
- Database schema
- Integration patterns

### 🚀 [Deployment](deployment/)
- Docker/Podman setup
- Kubernetes manifests
- Production deployment
- Monitoring setup

### 👤 [User Guide](user_guide/)
- Getting started
- Creating personalities
- Using the platform
- Troubleshooting

### 🛠️ [Developer Guide](developer_guide/)
- Development setup
- Contributing guidelines
- Code standards
- Testing procedures

## Quick Links

- [Main README](../README.md) - Project overview
- [API Documentation](../api/main.py) - Live API docs at `/docs`
- [Contributing](../CONTRIBUTING.md) - How to contribute
- [Changelog](../CHANGELOG.md) - Version history

## Getting Help

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/nestor-ai/nestor/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/nestor-ai/nestor/discussions)

## Documentation Standards

All documentation follows:
- Markdown format
- Clear, concise language
- Code examples where applicable
- Regular updates with code changes
