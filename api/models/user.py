"""
User model for authentication and user management.
"""
import uuid

from sqlalchemy import Column, String, Boolean, UUID
from sqlalchemy.orm import relationship

from .base import BaseModel


class User(BaseModel):
    """User model for authentication and profile management."""
    __tablename__ = "users"

    # Override id to use UUID
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )

    # Basic user information
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=True)

    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Profile settings
    preferred_language = Column(String(10), default="cs", nullable=False)
    timezone = Column(String(50), default="Europe/Prague", nullable=False)

    # Relationships
    personalities = relationship(
        "Personality",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    conversations = relationship(
        "Conversation",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"

    def to_dict(self):
        """Convert user to dictionary."""
        result = super().to_dict()
        # Convert UUID to string for JSON serialization
        result['id'] = str(result['id'])
        # Don't include password hash in dict
        result.pop('hashed_password', None)
        return result
