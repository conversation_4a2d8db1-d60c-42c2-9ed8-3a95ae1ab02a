"""
Document models for RAG (Retrieval-Augmented Generation) system.
"""
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, UUID, Integer
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector
from .base import BaseModel
import uuid

class Document(BaseModel):
    """Model for storing documents in the RAG system."""
    __tablename__ = "documents"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Document information
    title = Column(String(255), nullable=False)
    filename = Column(String(255), nullable=True)
    content = Column(Text, nullable=True)
    document_type = Column(String(50), nullable=True, index=True)

    # Foreign key to personality
    personality_id = Column(UUID(as_uuid=True), ForeignKey("personalities.id", ondelete="CASCADE"), nullable=True)

    # Metadata stored as JSONB
    document_metadata = Column(JSONB, default={}, nullable=False)

    # File information
    file_size = Column(Integer, nullable=True)
    file_hash = Column(String(64), nullable=True, index=True)

    # Processing status
    is_processed = Column(Boolean, default=False, nullable=False, index=True)

    # Relationships
    personality = relationship("Personality", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Document(id='{self.id}', title='{self.title}', personality_id='{self.personality_id}')>"

    def to_dict(self):
        """Convert document to dictionary."""
        result = super().to_dict()
        # Convert UUIDs to strings for JSON serialization
        result['id'] = str(result['id'])
        if result['personality_id']:
            result['personality_id'] = str(result['personality_id'])
        return result


class DocumentChunk(BaseModel):
    """Model for storing document chunks for vector search."""
    __tablename__ = "document_chunks"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Foreign key to document
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id", ondelete="CASCADE"), nullable=False)

    # Chunk content
    content = Column(Text, nullable=False)
    chunk_index = Column(Integer, nullable=False, index=True)

    # Metadata for chunk
    chunk_metadata = Column(JSONB, default={}, nullable=False)

    # Relationships
    document = relationship("Document", back_populates="chunks")
    embeddings = relationship("ChunkEmbedding", back_populates="chunk", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DocumentChunk(id='{self.id}', document_id='{self.document_id}', index={self.chunk_index})>"

    def to_dict(self):
        """Convert chunk to dictionary."""
        result = super().to_dict()
        # Convert UUIDs to strings for JSON serialization
        result['id'] = str(result['id'])
        result['document_id'] = str(result['document_id'])
        return result


class ChunkEmbedding(BaseModel):
    """Model for storing vector embeddings of document chunks."""
    __tablename__ = "chunk_embeddings"

    # Primary key is chunk_id (one-to-one relationship)
    chunk_id = Column(UUID(as_uuid=True), ForeignKey("document_chunks.id", ondelete="CASCADE"), primary_key=True)

    # Vector embedding (384 dimensions for sentence-transformers)
    embedding = Column(Vector(384), nullable=False)

    # Metadata for embedding
    embedding_metadata = Column(JSONB, default={}, nullable=False)

    # Relationships
    chunk = relationship("DocumentChunk", back_populates="embeddings")

    def __repr__(self):
        return f"<ChunkEmbedding(chunk_id='{self.chunk_id}')>"

    def to_dict(self):
        """Convert embedding to dictionary."""
        result = super().to_dict()
        # Convert UUID to string and exclude embedding vector from dict
        result['chunk_id'] = str(result['chunk_id'])
        # Don't include the actual embedding vector in dict (too large)
        result.pop('embedding', None)
        return result
