"""
Memory models for Memory Context Processor (MCP).
"""
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, UUID, Float, DateTime, CheckConstraint
from sqlalchemy.dialects.postgresql import JSON<PERSON>, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
from pgvector.sqlalchemy import Vector
from .base import BaseModel
import uuid
from datetime import datetime
from typing import List, Optional

class Memory(BaseModel):
    """Model for storing memories in the Memory Context Processor."""
    __tablename__ = "memories"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Content
    content = Column(Text, nullable=False)

    # Memory type with constraint
    memory_type = Column(
        String(20),
        nullable=False,
        index=True
    )
    __table_args__ = (
        CheckConstraint(
            "memory_type IN ('short_term', 'long_term', 'episodic', 'procedural', 'semantic', 'emotional')",
            name='check_memory_type'
        ),
    )

    # Foreign key to personality
    personality_id = Column(UUID(as_uuid=True), ForeignKey("personalities.id", ondelete="CASCADE"), nullable=True)

    # Metadata stored as JSONB
    memory_metadata = Column(JSONB, default={}, nullable=False)

    # Importance score (0.0 - 1.0)
    importance = Column(
        Float,
        default=0.5,
        nullable=False,
        index=True
    )
    __table_args__ = (
        CheckConstraint("importance >= 0.0 AND importance <= 1.0", name='check_importance_range'),
    )

    # Memory relationships
    parent_memory_id = Column(UUID(as_uuid=True), ForeignKey("memories.id"), nullable=True)
    related_memory_ids = Column(JSONB, default=[], nullable=False)

    # Expiration
    expires_at = Column(DateTime, nullable=True, index=True)

    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    accessed_at = Column(DateTime, nullable=True)

    # Relationships
    personality = relationship("Personality", back_populates="memories")
    parent_memory = relationship("Memory", remote_side=[id], backref="child_memories")
    embeddings = relationship("MemoryEmbedding", back_populates="memory", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Memory(id='{self.id}', type='{self.memory_type}', personality_id='{self.personality_id}')>"

    @hybrid_property
    def is_expired(self):
        """Check if memory is expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    def mark_accessed(self):
        """Mark memory as accessed."""
        self.accessed_at = datetime.utcnow()

    def to_dict(self):
        """Convert memory to dictionary."""
        result = super().to_dict()
        # Convert UUIDs to strings for JSON serialization
        result['id'] = str(result['id'])
        if result['personality_id']:
            result['personality_id'] = str(result['personality_id'])
        if result['parent_memory_id']:
            result['parent_memory_id'] = str(result['parent_memory_id'])
        return result


class MemoryEmbedding(BaseModel):
    """Model for storing vector embeddings of memories."""
    __tablename__ = "memory_embeddings"

    # Primary key is memory_id (one-to-one relationship)
    memory_id = Column(UUID(as_uuid=True), ForeignKey("memories.id", ondelete="CASCADE"), primary_key=True)

    # Vector embedding (384 dimensions for sentence-transformers)
    embedding = Column(Vector(384), nullable=False)

    # Metadata for embedding
    embedding_metadata = Column(JSONB, default={}, nullable=False)

    # Relationships
    memory = relationship("Memory", back_populates="embeddings")

    def __repr__(self):
        return f"<MemoryEmbedding(memory_id='{self.memory_id}')>"

    def to_dict(self):
        """Convert embedding to dictionary."""
        result = super().to_dict()
        # Convert UUID to string and exclude embedding vector from dict
        result['memory_id'] = str(result['memory_id'])
        # Don't include the actual embedding vector in dict (too large)
        result.pop('embedding', None)
        return result
