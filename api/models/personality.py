"""
Personality model for digital personalities.
"""
from sqlalchemy import <PERSON>um<PERSON>, String, Text, <PERSON><PERSON>an, ForeignKey, UUID
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from .base import BaseModel
import uuid

class Personality(BaseModel):
    """Model for digital personalities."""
    __tablename__ = "personalities"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)

    # Basic personality information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Metadata stored as JSONB
    personality_metadata = Column(JSONB, default={}, nullable=False)

    # Status
    is_active = Column(<PERSON><PERSON>an, default=True, nullable=False, index=True)

    # Relationships
    user = relationship("User", back_populates="personalities")
    memories = relationship("Memory", back_populates="personality", cascade="all, delete-orphan")
    documents = relationship("Document", back_populates="personality", cascade="all, delete-orphan")
    conversations = relationship("Conversation", back_populates="personality", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Personality(id='{self.id}', name='{self.name}', user_id='{self.user_id}')>"

    def to_dict(self):
        """Convert personality to dictionary."""
        result = super().to_dict()
        # Convert UUID to string for JSON serialization
        result['id'] = str(result['id'])
        result['user_id'] = str(result['user_id'])
        return result
