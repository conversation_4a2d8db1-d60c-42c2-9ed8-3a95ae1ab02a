"""
Conversation models for chat functionality.
"""
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, UUID, CheckConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from .base import BaseModel
import uuid

class Conversation(BaseModel):
    """Model for storing conversations between users and digital personalities."""
    __tablename__ = "conversations"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Foreign keys
    personality_id = Column(UUID(as_uuid=True), ForeignKey("personalities.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)

    # Conversation information
    title = Column(String(255), nullable=True)

    # Metadata stored as JSONB
    conversation_metadata = Column(JSONB, default={}, nullable=False)

    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Relationships
    personality = relationship("Personality", back_populates="conversations")
    user = relationship("User", back_populates="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Conversation(id='{self.id}', personality_id='{self.personality_id}', user_id='{self.user_id}')>"

    def to_dict(self):
        """Convert conversation to dictionary."""
        result = super().to_dict()
        # Convert UUIDs to strings for JSON serialization
        result['id'] = str(result['id'])
        result['personality_id'] = str(result['personality_id'])
        result['user_id'] = str(result['user_id'])
        return result


class ConversationMessage(BaseModel):
    """Model for storing individual messages in conversations."""
    __tablename__ = "conversation_messages"

    # Override id to use UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Foreign key to conversation
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False)

    # Message information
    role = Column(String(20), nullable=False, index=True)
    content = Column(Text, nullable=False)

    # Role constraint
    __table_args__ = (
        CheckConstraint(
            "role IN ('user', 'assistant', 'system')",
            name='check_message_role'
        ),
    )

    # Metadata stored as JSONB
    message_metadata = Column(JSONB, default={}, nullable=False)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")

    def __repr__(self):
        return f"<ConversationMessage(id='{self.id}', conversation_id='{self.conversation_id}', role='{self.role}')>"

    def to_dict(self):
        """Convert message to dictionary."""
        result = super().to_dict()
        # Convert UUIDs to strings for JSON serialization
        result['id'] = str(result['id'])
        result['conversation_id'] = str(result['conversation_id'])
        return result
