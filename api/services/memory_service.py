"""
Memory service for managing memory operations.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from ..models.memory import Memory, MemoryEmbedding
from ..schemas.memory import MemoryCreate, MemoryUpdate


class MemoryService:
    """Service for memory operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_memory_by_id(self, memory_id: UUID) -> Optional[Memory]:
        """Get memory by ID."""
        return self.db.query(Memory).filter(Memory.id == memory_id).first()
    
    def get_memories_by_personality(
        self, 
        personality_id: UUID, 
        skip: int = 0, 
        limit: int = 100,
        memory_type: Optional[str] = None,
        include_inactive: bool = False
    ) -> List[Memory]:
        """Get memories for a specific personality."""
        query = self.db.query(Memory).filter(
            Memory.personality_id == personality_id
        )
        
        if memory_type:
            query = query.filter(Memory.memory_type == memory_type)
        
        if not include_inactive:
            query = query.filter(Memory.is_active == True)
        
        return query.order_by(Memory.created_at.desc()).offset(skip).limit(limit).all()
    
    def create_memory(self, memory_create: MemoryCreate) -> Memory:
        """Create a new memory."""
        db_memory = Memory(
            content=memory_create.content,
            memory_type=memory_create.memory_type,
            personality_id=memory_create.personality_id,
            metadata=memory_create.metadata,
            importance=memory_create.importance,
            parent_memory_id=memory_create.parent_memory_id,
            expires_at=memory_create.expires_at
        )
        
        self.db.add(db_memory)
        self.db.commit()
        self.db.refresh(db_memory)
        
        return db_memory
    
    def update_memory(
        self, 
        memory_id: UUID, 
        memory_update: MemoryUpdate
    ) -> Optional[Memory]:
        """Update memory information."""
        db_memory = self.get_memory_by_id(memory_id)
        if not db_memory:
            return None
        
        update_data = memory_update.model_dump(exclude_unset=True)
        
        # Update memory fields
        for field, value in update_data.items():
            setattr(db_memory, field, value)
        
        self.db.commit()
        self.db.refresh(db_memory)
        
        return db_memory
    
    def delete_memory(self, memory_id: UUID) -> bool:
        """Delete a memory."""
        db_memory = self.get_memory_by_id(memory_id)
        if not db_memory:
            return False
        
        self.db.delete(db_memory)
        self.db.commit()
        
        return True
    
    def mark_memory_accessed(self, memory_id: UUID) -> Optional[Memory]:
        """Mark memory as accessed."""
        db_memory = self.get_memory_by_id(memory_id)
        if not db_memory:
            return None
        
        db_memory.mark_accessed()
        self.db.commit()
        self.db.refresh(db_memory)
        
        return db_memory
    
    def get_memory_embedding(self, memory_id: UUID) -> Optional[MemoryEmbedding]:
        """Get memory embedding."""
        return self.db.query(MemoryEmbedding).filter(
            MemoryEmbedding.memory_id == memory_id
        ).first()
    
    def create_memory_embedding(
        self, 
        memory_id: UUID, 
        embedding: List[float],
        metadata: dict = None
    ) -> MemoryEmbedding:
        """Create memory embedding."""
        db_embedding = MemoryEmbedding(
            memory_id=memory_id,
            embedding=embedding,
            metadata=metadata or {}
        )
        
        self.db.add(db_embedding)
        self.db.commit()
        self.db.refresh(db_embedding)
        
        return db_embedding
    
    def update_memory_embedding(
        self, 
        memory_id: UUID, 
        embedding: List[float],
        metadata: dict = None
    ) -> Optional[MemoryEmbedding]:
        """Update memory embedding."""
        db_embedding = self.get_memory_embedding(memory_id)
        if not db_embedding:
            return self.create_memory_embedding(memory_id, embedding, metadata)
        
        db_embedding.embedding = embedding
        if metadata:
            db_embedding.metadata = metadata
        
        self.db.commit()
        self.db.refresh(db_embedding)
        
        return db_embedding
    
    def search_memories_by_similarity(
        self,
        query_embedding: List[float],
        personality_id: Optional[UUID] = None,
        memory_types: Optional[List[str]] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[tuple[Memory, float]]:
        """Search memories by vector similarity."""
        # This would use pgvector for similarity search
        # For now, return empty list as placeholder
        # TODO: Implement vector similarity search
        return []
    
    def get_memory_stats(self, personality_id: UUID) -> dict:
        """Get memory statistics for a personality."""
        total_memories = self.db.query(Memory).filter(
            Memory.personality_id == personality_id,
            Memory.is_active == True
        ).count()
        
        # Count by type
        memories_by_type = {}
        for memory_type in ["short_term", "long_term", "episodic", "procedural", "semantic", "emotional"]:
            count = self.db.query(Memory).filter(
                Memory.personality_id == personality_id,
                Memory.memory_type == memory_type,
                Memory.is_active == True
            ).count()
            memories_by_type[memory_type] = count
        
        # Average importance
        avg_importance = self.db.query(Memory).filter(
            Memory.personality_id == personality_id,
            Memory.is_active == True
        ).with_entities(Memory.importance).all()
        
        if avg_importance:
            avg_importance = sum(imp[0] for imp in avg_importance) / len(avg_importance)
        else:
            avg_importance = 0.0
        
        return {
            "total_memories": total_memories,
            "memories_by_type": memories_by_type,
            "average_importance": avg_importance
        }
