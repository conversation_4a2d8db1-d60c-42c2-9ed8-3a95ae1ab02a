"""
User service for managing user operations.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from passlib.context import CryptContext

from models.user import User
from schemas.user import UserCreate, UserUpdate


class UserService:
    """Service for user operations."""

    def __init__(self, db: Session):
        self.db = db
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def get_password_hash(self, password: str) -> str:
        """Hash a password."""
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        return self.db.query(User).filter(User.id == user_id).first()

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return self.db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()

    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get list of users."""
        return self.db.query(User).offset(skip).limit(limit).all()

    def create_user(self, user_create: UserCreate) -> User:
        """Create a new user."""
        # Check if username or email already exists
        if self.get_user_by_username(user_create.username):
            raise ValueError(f"Username '{user_create.username}' already exists")

        if self.get_user_by_email(user_create.email):
            raise ValueError(f"Email '{user_create.email}' already exists")

        # Create user with hashed password
        hashed_password = self.get_password_hash(user_create.password)

        db_user = User(
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=hashed_password,
            preferred_language=user_create.preferred_language,
            timezone=user_create.timezone
        )

        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)

        return db_user

    def update_user(self, user_id: UUID, user_update: UserUpdate) -> Optional[User]:
        """Update user information."""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return None

        update_data = user_update.model_dump(exclude_unset=True)

        # Handle password update separately
        if "password" in update_data:
            update_data["hashed_password"] = self.get_password_hash(
                update_data.pop("password")
            )

        # Check for username/email conflicts
        if "username" in update_data:
            existing_user = self.get_user_by_username(update_data["username"])
            if existing_user and existing_user.id != user_id:
                raise ValueError(
                    f"Username '{update_data['username']}' already exists"
                )

        if "email" in update_data:
            existing_user = self.get_user_by_email(update_data["email"])
            if existing_user and existing_user.id != user_id:
                raise ValueError(f"Email '{update_data['email']}' already exists")

        # Update user fields
        for field, value in update_data.items():
            setattr(db_user, field, value)

        self.db.commit()
        self.db.refresh(db_user)

        return db_user

    def delete_user(self, user_id: UUID) -> bool:
        """Delete a user."""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return False

        self.db.delete(db_user)
        self.db.commit()

        return True

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password."""
        user = self.get_user_by_username(username)
        if not user:
            return None

        if not self.verify_password(password, user.hashed_password):
            return None

        return user

    def activate_user(self, user_id: UUID) -> Optional[User]:
        """Activate a user account."""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return None

        db_user.is_active = True
        self.db.commit()
        self.db.refresh(db_user)

        return db_user

    def deactivate_user(self, user_id: UUID) -> Optional[User]:
        """Deactivate a user account."""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return None

        db_user.is_active = False
        self.db.commit()
        self.db.refresh(db_user)

        return db_user

    def verify_user(self, user_id: UUID) -> Optional[User]:
        """Verify a user account."""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return None

        db_user.is_verified = True
        self.db.commit()
        self.db.refresh(db_user)

        return db_user
