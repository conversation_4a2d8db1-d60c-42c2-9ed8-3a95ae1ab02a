"""
Document service for managing document operations.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from ..models.document import Document, DocumentChunk, ChunkEmbedding
from ..schemas.document import DocumentCreate, DocumentUpdate


class DocumentService:
    """Service for document operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_document_by_id(self, document_id: UUID) -> Optional[Document]:
        """Get document by ID."""
        return self.db.query(Document).filter(Document.id == document_id).first()
    
    def get_documents_by_personality(
        self, 
        personality_id: UUID, 
        skip: int = 0, 
        limit: int = 100,
        document_type: Optional[str] = None
    ) -> List[Document]:
        """Get documents for a specific personality."""
        query = self.db.query(Document).filter(
            Document.personality_id == personality_id
        )
        
        if document_type:
            query = query.filter(Document.document_type == document_type)
        
        return query.order_by(Document.created_at.desc()).offset(skip).limit(limit).all()
    
    def create_document(self, document_create: DocumentCreate) -> Document:
        """Create a new document."""
        db_document = Document(
            title=document_create.title,
            filename=document_create.filename,
            content=document_create.content,
            document_type=document_create.document_type,
            personality_id=document_create.personality_id,
            metadata=document_create.metadata
        )
        
        self.db.add(db_document)
        self.db.commit()
        self.db.refresh(db_document)
        
        return db_document
    
    def update_document(
        self, 
        document_id: UUID, 
        document_update: DocumentUpdate
    ) -> Optional[Document]:
        """Update document information."""
        db_document = self.get_document_by_id(document_id)
        if not db_document:
            return None
        
        update_data = document_update.model_dump(exclude_unset=True)
        
        # Update document fields
        for field, value in update_data.items():
            setattr(db_document, field, value)
        
        self.db.commit()
        self.db.refresh(db_document)
        
        return db_document
    
    def delete_document(self, document_id: UUID) -> bool:
        """Delete a document."""
        db_document = self.get_document_by_id(document_id)
        if not db_document:
            return False
        
        self.db.delete(db_document)
        self.db.commit()
        
        return True
    
    def mark_document_processed(self, document_id: UUID) -> Optional[Document]:
        """Mark document as processed."""
        db_document = self.get_document_by_id(document_id)
        if not db_document:
            return None
        
        db_document.is_processed = True
        self.db.commit()
        self.db.refresh(db_document)
        
        return db_document
    
    def get_document_chunks(self, document_id: UUID) -> List[DocumentChunk]:
        """Get chunks for a document."""
        return self.db.query(DocumentChunk).filter(
            DocumentChunk.document_id == document_id
        ).order_by(DocumentChunk.chunk_index).all()
    
    def create_document_chunk(
        self, 
        document_id: UUID, 
        content: str, 
        chunk_index: int,
        metadata: dict = None
    ) -> DocumentChunk:
        """Create a document chunk."""
        db_chunk = DocumentChunk(
            document_id=document_id,
            content=content,
            chunk_index=chunk_index,
            metadata=metadata or {}
        )
        
        self.db.add(db_chunk)
        self.db.commit()
        self.db.refresh(db_chunk)
        
        return db_chunk
    
    def get_chunk_embedding(self, chunk_id: UUID) -> Optional[ChunkEmbedding]:
        """Get chunk embedding."""
        return self.db.query(ChunkEmbedding).filter(
            ChunkEmbedding.chunk_id == chunk_id
        ).first()
    
    def create_chunk_embedding(
        self, 
        chunk_id: UUID, 
        embedding: List[float],
        metadata: dict = None
    ) -> ChunkEmbedding:
        """Create chunk embedding."""
        db_embedding = ChunkEmbedding(
            chunk_id=chunk_id,
            embedding=embedding,
            metadata=metadata or {}
        )
        
        self.db.add(db_embedding)
        self.db.commit()
        self.db.refresh(db_embedding)
        
        return db_embedding
    
    def search_chunks_by_similarity(
        self,
        query_embedding: List[float],
        personality_id: Optional[UUID] = None,
        document_types: Optional[List[str]] = None,
        top_k: int = 10,
        similarity_threshold: float = 0.6
    ) -> List[tuple[DocumentChunk, float]]:
        """Search document chunks by vector similarity."""
        # This would use pgvector for similarity search
        # For now, return empty list as placeholder
        # TODO: Implement vector similarity search
        return []
    
    def get_document_stats(self, personality_id: UUID) -> dict:
        """Get document statistics for a personality."""
        total_documents = self.db.query(Document).filter(
            Document.personality_id == personality_id
        ).count()
        
        processed_documents = self.db.query(Document).filter(
            Document.personality_id == personality_id,
            Document.is_processed == True
        ).count()
        
        # Count by type
        documents_by_type = {}
        document_types = self.db.query(Document.document_type).filter(
            Document.personality_id == personality_id,
            Document.document_type.isnot(None)
        ).distinct().all()
        
        for doc_type in document_types:
            count = self.db.query(Document).filter(
                Document.personality_id == personality_id,
                Document.document_type == doc_type[0]
            ).count()
            documents_by_type[doc_type[0]] = count
        
        return {
            "total_documents": total_documents,
            "processed_documents": processed_documents,
            "documents_by_type": documents_by_type
        }
