"""
Personality service for managing digital personality operations.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from ..models.personality import Personality
from ..schemas.personality import PersonalityCreate, PersonalityUpdate


class PersonalityService:
    """Service for personality operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_personality_by_id(self, personality_id: UUID) -> Optional[Personality]:
        """Get personality by ID."""
        return self.db.query(Personality).filter(
            Personality.id == personality_id
        ).first()
    
    def get_personalities_by_user(
        self, 
        user_id: UUID, 
        skip: int = 0, 
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[Personality]:
        """Get personalities for a specific user."""
        query = self.db.query(Personality).filter(Personality.user_id == user_id)
        
        if not include_inactive:
            query = query.filter(Personality.is_active == True)
        
        return query.offset(skip).limit(limit).all()
    
    def get_personalities(
        self, 
        skip: int = 0, 
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[Personality]:
        """Get list of personalities."""
        query = self.db.query(Personality)
        
        if not include_inactive:
            query = query.filter(Personality.is_active == True)
        
        return query.offset(skip).limit(limit).all()
    
    def create_personality(
        self, 
        user_id: UUID, 
        personality_create: PersonalityCreate
    ) -> Personality:
        """Create a new personality."""
        db_personality = Personality(
            user_id=user_id,
            name=personality_create.name,
            description=personality_create.description,
            metadata=personality_create.metadata
        )
        
        self.db.add(db_personality)
        self.db.commit()
        self.db.refresh(db_personality)
        
        return db_personality
    
    def update_personality(
        self, 
        personality_id: UUID, 
        personality_update: PersonalityUpdate
    ) -> Optional[Personality]:
        """Update personality information."""
        db_personality = self.get_personality_by_id(personality_id)
        if not db_personality:
            return None
        
        update_data = personality_update.model_dump(exclude_unset=True)
        
        # Update personality fields
        for field, value in update_data.items():
            setattr(db_personality, field, value)
        
        self.db.commit()
        self.db.refresh(db_personality)
        
        return db_personality
    
    def delete_personality(self, personality_id: UUID) -> bool:
        """Delete a personality."""
        db_personality = self.get_personality_by_id(personality_id)
        if not db_personality:
            return False
        
        self.db.delete(db_personality)
        self.db.commit()
        
        return True
    
    def activate_personality(self, personality_id: UUID) -> Optional[Personality]:
        """Activate a personality."""
        db_personality = self.get_personality_by_id(personality_id)
        if not db_personality:
            return None
        
        db_personality.is_active = True
        self.db.commit()
        self.db.refresh(db_personality)
        
        return db_personality
    
    def deactivate_personality(self, personality_id: UUID) -> Optional[Personality]:
        """Deactivate a personality."""
        db_personality = self.get_personality_by_id(personality_id)
        if not db_personality:
            return None
        
        db_personality.is_active = False
        self.db.commit()
        self.db.refresh(db_personality)
        
        return db_personality
    
    def check_user_ownership(self, personality_id: UUID, user_id: UUID) -> bool:
        """Check if user owns the personality."""
        personality = self.get_personality_by_id(personality_id)
        if not personality:
            return False
        
        return personality.user_id == user_id
    
    def get_personality_stats(self, personality_id: UUID) -> dict:
        """Get statistics for a personality."""
        from ..models.memory import Memory
        from ..models.document import Document
        from ..models.conversation import Conversation
        
        personality = self.get_personality_by_id(personality_id)
        if not personality:
            return {}
        
        # Count memories
        memory_count = self.db.query(Memory).filter(
            Memory.personality_id == personality_id,
            Memory.is_active == True
        ).count()
        
        # Count documents
        document_count = self.db.query(Document).filter(
            Document.personality_id == personality_id
        ).count()
        
        # Count conversations
        conversation_count = self.db.query(Conversation).filter(
            Conversation.personality_id == personality_id,
            Conversation.is_active == True
        ).count()
        
        # Get last interaction (most recent conversation)
        last_conversation = self.db.query(Conversation).filter(
            Conversation.personality_id == personality_id,
            Conversation.is_active == True
        ).order_by(Conversation.updated_at.desc()).first()
        
        last_interaction = None
        if last_conversation:
            last_interaction = last_conversation.updated_at
        
        return {
            "memory_count": memory_count,
            "document_count": document_count,
            "conversation_count": conversation_count,
            "last_interaction": last_interaction
        }
