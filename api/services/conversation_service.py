"""
Conversation service for managing conversation operations.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from ..models.conversation import Conversation, ConversationMessage
from ..schemas.conversation import ConversationCreate, ConversationUpdate, MessageCreate


class ConversationService:
    """Service for conversation operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_conversation_by_id(self, conversation_id: UUID) -> Optional[Conversation]:
        """Get conversation by ID."""
        return self.db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()
    
    def get_conversations_by_user(
        self, 
        user_id: UUID, 
        skip: int = 0, 
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[Conversation]:
        """Get conversations for a specific user."""
        query = self.db.query(Conversation).filter(Conversation.user_id == user_id)
        
        if not include_inactive:
            query = query.filter(Conversation.is_active == True)
        
        return query.order_by(Conversation.updated_at.desc()).offset(skip).limit(limit).all()
    
    def get_conversations_by_personality(
        self, 
        personality_id: UUID, 
        skip: int = 0, 
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[Conversation]:
        """Get conversations for a specific personality."""
        query = self.db.query(Conversation).filter(
            Conversation.personality_id == personality_id
        )
        
        if not include_inactive:
            query = query.filter(Conversation.is_active == True)
        
        return query.order_by(Conversation.updated_at.desc()).offset(skip).limit(limit).all()
    
    def create_conversation(
        self, 
        user_id: UUID, 
        conversation_create: ConversationCreate
    ) -> Conversation:
        """Create a new conversation."""
        db_conversation = Conversation(
            personality_id=conversation_create.personality_id,
            user_id=user_id,
            title=conversation_create.title,
            metadata=conversation_create.metadata
        )
        
        self.db.add(db_conversation)
        self.db.commit()
        self.db.refresh(db_conversation)
        
        return db_conversation
    
    def update_conversation(
        self, 
        conversation_id: UUID, 
        conversation_update: ConversationUpdate
    ) -> Optional[Conversation]:
        """Update conversation information."""
        db_conversation = self.get_conversation_by_id(conversation_id)
        if not db_conversation:
            return None
        
        update_data = conversation_update.model_dump(exclude_unset=True)
        
        # Update conversation fields
        for field, value in update_data.items():
            setattr(db_conversation, field, value)
        
        self.db.commit()
        self.db.refresh(db_conversation)
        
        return db_conversation
    
    def delete_conversation(self, conversation_id: UUID) -> bool:
        """Delete a conversation."""
        db_conversation = self.get_conversation_by_id(conversation_id)
        if not db_conversation:
            return False
        
        self.db.delete(db_conversation)
        self.db.commit()
        
        return True
    
    def get_conversation_messages(
        self, 
        conversation_id: UUID, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[ConversationMessage]:
        """Get messages for a conversation."""
        return self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.created_at).offset(skip).limit(limit).all()
    
    def add_message(
        self, 
        conversation_id: UUID, 
        message_create: MessageCreate
    ) -> ConversationMessage:
        """Add a message to a conversation."""
        db_message = ConversationMessage(
            conversation_id=conversation_id,
            role=message_create.role,
            content=message_create.content,
            metadata=message_create.metadata
        )
        
        self.db.add(db_message)
        self.db.commit()
        self.db.refresh(db_message)
        
        # Update conversation's updated_at timestamp
        conversation = self.get_conversation_by_id(conversation_id)
        if conversation:
            self.db.commit()  # This will trigger the updated_at update
        
        return db_message
    
    def get_message_by_id(self, message_id: UUID) -> Optional[ConversationMessage]:
        """Get message by ID."""
        return self.db.query(ConversationMessage).filter(
            ConversationMessage.id == message_id
        ).first()
    
    def check_user_ownership(self, conversation_id: UUID, user_id: UUID) -> bool:
        """Check if user owns the conversation."""
        conversation = self.get_conversation_by_id(conversation_id)
        if not conversation:
            return False
        
        return conversation.user_id == user_id
    
    def get_conversation_with_messages(
        self, 
        conversation_id: UUID
    ) -> Optional[tuple[Conversation, List[ConversationMessage]]]:
        """Get conversation with its messages."""
        conversation = self.get_conversation_by_id(conversation_id)
        if not conversation:
            return None
        
        messages = self.get_conversation_messages(conversation_id)
        return conversation, messages
    
    def get_recent_messages(
        self, 
        conversation_id: UUID, 
        limit: int = 10
    ) -> List[ConversationMessage]:
        """Get recent messages from a conversation."""
        return self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.created_at.desc()).limit(limit).all()
    
    def get_conversation_stats(self, personality_id: UUID) -> dict:
        """Get conversation statistics for a personality."""
        total_conversations = self.db.query(Conversation).filter(
            Conversation.personality_id == personality_id,
            Conversation.is_active == True
        ).count()
        
        total_messages = self.db.query(ConversationMessage).join(Conversation).filter(
            Conversation.personality_id == personality_id,
            Conversation.is_active == True
        ).count()
        
        # Get most recent conversation
        recent_conversation = self.db.query(Conversation).filter(
            Conversation.personality_id == personality_id,
            Conversation.is_active == True
        ).order_by(Conversation.updated_at.desc()).first()
        
        last_interaction = None
        if recent_conversation:
            last_interaction = recent_conversation.updated_at
        
        return {
            "total_conversations": total_conversations,
            "total_messages": total_messages,
            "last_interaction": last_interaction
        }
