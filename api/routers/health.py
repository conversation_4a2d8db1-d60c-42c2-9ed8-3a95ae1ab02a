"""
Health check router for API monitoring.
"""
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter
# Temporarily disable database dependencies
# from sqlalchemy.orm import Session
# from models.base import get_db

router = APIRouter(prefix="/health", tags=["health"])


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "nestor-api",
        "version": "1.0.0"
    }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check - simplified version."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "nestor-api",
        "version": "1.0.0",
        "checks": {
            "database": {
                "status": "disabled",
                "message": "Database check temporarily disabled"
            }
        }
    }

    return health_status


@router.get("/ready")
async def readiness_check() -> Dict[str, Any]:
    """Readiness check for Kubernetes - simplified version."""
    return {"status": "ready"}


@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """Liveness check for Kubernetes."""
    return {"status": "alive"}
