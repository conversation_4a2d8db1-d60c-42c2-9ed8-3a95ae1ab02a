"""
Personalities router for digital personality management.
"""
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from models.base import get_db
from models.user import User
from schemas.personality import (
    PersonalityCreate,
    PersonalityUpdate,
    PersonalityResponse,
    PersonalityWithStats
)
from services.personality_service import PersonalityService
from routers.auth import get_current_active_user

router = APIRouter(prefix="/personalities", tags=["personalities"])


@router.get("/", response_model=List[PersonalityResponse])
async def get_user_personalities(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    include_inactive: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get personalities for the current user."""
    personality_service = PersonalityService(db)
    personalities = personality_service.get_personalities_by_user(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        include_inactive=include_inactive
    )
    return personalities


@router.post("/", response_model=PersonalityResponse)
async def create_personality(
    personality_create: PersonalityCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new personality."""
    personality_service = PersonalityService(db)
    personality = personality_service.create_personality(
        user_id=current_user.id,
        personality_create=personality_create
    )
    return personality


@router.get("/{personality_id}", response_model=PersonalityResponse)
async def get_personality(
    personality_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific personality."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this personality"
        )

    personality = personality_service.get_personality_by_id(personality_id)
    if not personality:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    return personality


@router.put("/{personality_id}", response_model=PersonalityResponse)
async def update_personality(
    personality_id: UUID,
    personality_update: PersonalityUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a personality."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this personality"
        )

    updated_personality = personality_service.update_personality(
        personality_id, personality_update
    )
    if not updated_personality:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    return updated_personality


@router.delete("/{personality_id}")
async def delete_personality(
    personality_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a personality."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this personality"
        )

    success = personality_service.delete_personality(personality_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    return {"message": "Personality deleted successfully"}


@router.get("/{personality_id}/stats", response_model=PersonalityWithStats)
async def get_personality_stats(
    personality_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get personality statistics."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this personality"
        )

    personality = personality_service.get_personality_by_id(personality_id)
    if not personality:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    stats = personality_service.get_personality_stats(personality_id)

    # Convert personality to dict and add stats
    personality_dict = personality.to_dict()
    personality_dict.update(stats)

    return PersonalityWithStats(**personality_dict)


@router.post("/{personality_id}/activate")
async def activate_personality(
    personality_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Activate a personality."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to activate this personality"
        )

    updated_personality = personality_service.activate_personality(personality_id)
    if not updated_personality:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    return {"message": "Personality activated successfully"}


@router.post("/{personality_id}/deactivate")
async def deactivate_personality(
    personality_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Deactivate a personality."""
    personality_service = PersonalityService(db)

    # Check if user owns the personality
    if not personality_service.check_user_ownership(personality_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to deactivate this personality"
        )

    updated_personality = personality_service.deactivate_personality(personality_id)
    if not updated_personality:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personality not found"
        )

    return {"message": "Personality deactivated successfully"}
