# Pydantic schemas for NESTOR API
from .user import UserCreate, UserUpdate, UserResponse
from .personality import PersonalityCreate, PersonalityUpdate, PersonalityResponse
from .memory import MemoryCreate, MemoryUpdate, MemoryResponse, MemoryQuery
from .document import DocumentCreate, DocumentUpdate, DocumentResponse
from .conversation import ConversationCreate, ConversationResponse, MessageCreate, MessageResponse

__all__ = [
    "UserCreate",
    "UserUpdate", 
    "UserResponse",
    "PersonalityCreate",
    "PersonalityUpdate",
    "PersonalityResponse",
    "MemoryCreate",
    "MemoryUpdate",
    "MemoryResponse",
    "MemoryQuery",
    "DocumentCreate",
    "DocumentUpdate",
    "DocumentResponse",
    "ConversationCreate",
    "ConversationResponse",
    "MessageCreate",
    "MessageResponse"
]
