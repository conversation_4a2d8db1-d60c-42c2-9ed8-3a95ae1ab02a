"""
Pydantic schemas for Conversation model.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class MessageRole:
    """Message role constants."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

    @classmethod
    def all(cls) -> List[str]:
        return [cls.USER, cls.ASSISTANT, cls.SYSTEM]


class ConversationBase(BaseModel):
    """Base conversation schema with common fields."""
    title: Optional[str] = Field(None, max_length=255)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ConversationCreate(ConversationBase):
    """Schema for creating a new conversation."""
    personality_id: UUID


class ConversationUpdate(BaseModel):
    """Schema for updating conversation information."""
    title: Optional[str] = Field(None, max_length=255)
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class ConversationResponse(ConversationBase):
    """Schema for conversation response."""
    id: UUID
    personality_id: UUID
    user_id: UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MessageBase(BaseModel):
    """Base message schema with common fields."""
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., min_length=1)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class MessageCreate(MessageBase):
    """Schema for creating a new message."""
    pass


class MessageResponse(MessageBase):
    """Schema for message response."""
    id: UUID
    conversation_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class ConversationWithMessages(ConversationResponse):
    """Schema for conversation with messages."""
    messages: List[MessageResponse] = Field(default_factory=list)


class ChatRequest(BaseModel):
    """Schema for chat request."""
    message: str = Field(..., min_length=1, description="User message")
    personality_id: UUID = Field(..., description="ID of personality to chat with")
    conversation_id: Optional[UUID] = Field(
        None, 
        description="Existing conversation ID (optional)"
    )
    context: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Additional context"
    )


class ChatResponse(BaseModel):
    """Schema for chat response."""
    message: str
    conversation_id: UUID
    personality_id: UUID
    context: Dict[str, Any] = Field(default_factory=dict)
    sources: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Sources used for response generation"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict)
