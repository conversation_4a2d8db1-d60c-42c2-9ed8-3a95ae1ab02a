"""
Pydantic schemas for Memory model.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class MemoryType:
    """Memory type constants."""
    SHORT_TERM = "short_term"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"
    PROCEDURAL = "procedural"
    SEMANTIC = "semantic"
    EMOTIONAL = "emotional"

    @classmethod
    def all(cls) -> List[str]:
        return [
            cls.SHORT_TERM,
            cls.LONG_TERM,
            cls.EPISODIC,
            cls.PROCEDURAL,
            cls.SEMANTIC,
            cls.EMOTIONAL
        ]


class MemoryBase(BaseModel):
    """Base memory schema with common fields."""
    content: str = Field(..., min_length=1)
    memory_type: str = Field(..., description="Type of memory")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    importance: float = Field(default=0.5, ge=0.0, le=1.0)


class MemoryCreate(MemoryBase):
    """Schema for creating a new memory."""
    personality_id: Optional[UUID] = None
    parent_memory_id: Optional[UUID] = None
    expires_at: Optional[datetime] = None


class MemoryUpdate(BaseModel):
    """Schema for updating memory information."""
    content: Optional[str] = Field(None, min_length=1)
    importance: Optional[float] = Field(None, ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None


class MemoryResponse(MemoryBase):
    """Schema for memory response."""
    id: UUID
    personality_id: Optional[UUID]
    parent_memory_id: Optional[UUID]
    related_memory_ids: List[UUID] = Field(default_factory=list)
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    accessed_at: Optional[datetime]

    class Config:
        from_attributes = True


class MemoryQuery(BaseModel):
    """Schema for memory search query."""
    query: str = Field(..., min_length=1, description="Search query")
    memory_types: Optional[List[str]] = Field(
        None, 
        description="Types of memory to search"
    )
    personality_id: Optional[UUID] = None
    top_k: int = Field(default=5, ge=1, le=50)
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    importance_threshold: float = Field(default=0.0, ge=0.0, le=1.0)
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    include_inactive: bool = Field(default=False)
    include_expired: bool = Field(default=False)


class MemorySearchResult(BaseModel):
    """Schema for memory search result."""
    memory: MemoryResponse
    similarity_score: float = Field(ge=0.0, le=1.0)
    relevance_score: float = Field(ge=0.0, le=1.0)
    context_match: bool = False


class MemorySearchResponse(BaseModel):
    """Schema for memory search response."""
    query: str
    results: List[MemorySearchResult]
    total_results: int
    metadata: Dict[str, Any] = Field(default_factory=dict)
