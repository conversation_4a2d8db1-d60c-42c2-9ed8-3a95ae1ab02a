"""
Pydantic schemas for Document model.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class DocumentBase(BaseModel):
    """Base document schema with common fields."""
    title: str = Field(..., min_length=1, max_length=255)
    filename: Optional[str] = Field(None, max_length=255)
    document_type: Optional[str] = Field(None, max_length=50)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DocumentCreate(DocumentBase):
    """Schema for creating a new document."""
    content: Optional[str] = None
    personality_id: Optional[UUID] = None


class DocumentUpdate(BaseModel):
    """Schema for updating document information."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = None
    document_type: Optional[str] = Field(None, max_length=50)
    metadata: Optional[Dict[str, Any]] = None
    is_processed: Optional[bool] = None


class DocumentResponse(DocumentBase):
    """Schema for document response."""
    id: UUID
    personality_id: Optional[UUID]
    file_size: Optional[int]
    file_hash: Optional[str]
    is_processed: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DocumentChunkResponse(BaseModel):
    """Schema for document chunk response."""
    id: UUID
    document_id: UUID
    content: str
    chunk_index: int
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime

    class Config:
        from_attributes = True


class DocumentWithChunks(DocumentResponse):
    """Schema for document with chunks."""
    chunks: List[DocumentChunkResponse] = Field(default_factory=list)


class DocumentSearchQuery(BaseModel):
    """Schema for document search query."""
    query: str = Field(..., min_length=1, description="Search query")
    personality_id: Optional[UUID] = None
    document_types: Optional[List[str]] = None
    top_k: int = Field(default=10, ge=1, le=50)
    similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)


class DocumentSearchResult(BaseModel):
    """Schema for document search result."""
    chunk: DocumentChunkResponse
    similarity_score: float = Field(ge=0.0, le=1.0)
    document: DocumentResponse


class DocumentSearchResponse(BaseModel):
    """Schema for document search response."""
    query: str
    results: List[DocumentSearchResult]
    total_results: int
    metadata: Dict[str, Any] = Field(default_factory=dict)
