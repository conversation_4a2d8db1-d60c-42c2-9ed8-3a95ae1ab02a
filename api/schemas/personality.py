"""
Pydantic schemas for Personality model.
"""
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class PersonalityBase(BaseModel):
    """Base personality schema with common fields."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class PersonalityCreate(PersonalityBase):
    """Schema for creating a new personality."""
    pass


class PersonalityUpdate(BaseModel):
    """Schema for updating personality information."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class PersonalityResponse(PersonalityBase):
    """Schema for personality response."""
    id: UUID
    user_id: UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PersonalityWithStats(PersonalityResponse):
    """Schema for personality with statistics."""
    memory_count: int = 0
    document_count: int = 0
    conversation_count: int = 0
    last_interaction: Optional[datetime] = None
