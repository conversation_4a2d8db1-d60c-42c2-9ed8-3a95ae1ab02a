"""
Pydantic schemas pro Imprint model - rozšíření souč<PERSON>ných schémat o imprint struktury

Toto schéma definuje API struktury pro práci s LoRA imprinty digitálních osobností
v rámci NESTOR persona_core modulu.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field


class ImprintType(str, Enum):
    """Typy LoRA imprintů"""
    STYLE = "style"
    EMOTIONAL = "emotional"
    BEHAVIORAL = "behavioral"
    CONVERSATIONAL = "conversational"
    DOMAIN_SPECIFIC = "domain_specific"
    COMPOSITE = "composite"


class ImprintStatus(str, Enum):
    """Stavy LoRA imprintu"""
    TRAINING = "training"
    READY = "ready"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    FAILED = "failed"


class EmotionalState(str, Enum):
    """Emoční stavy pro imprint kontext"""
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    TRUST = "trust"
    ANTICIPATION = "anticipation"
    NEUTRAL = "neutral"
    CONTEMPLATIVE = "contemplative"
    PLAYFUL = "playful"
    MELANCHOLIC = "melancholic"


class BehavioralTrait(str, Enum):
    """Behaviorální rysy pro imprint"""
    SUBMISSIVE = "submissive"
    DOMINANT = "dominant"
    DREAMY = "dreamy"
    PRACTICAL = "practical"
    SENSUAL = "sensual"
    INTELLECTUAL = "intellectual"
    OBEDIENT = "obedient"
    REBELLIOUS = "rebellious"
    CARING = "caring"
    INDEPENDENT = "independent"
    ROMANTIC = "romantic"
    ANALYTICAL = "analytical"


class ImprintBase(BaseModel):
    """Základní schéma pro imprint"""
    name: str = Field(..., min_length=1, max_length=255)
    imprint_type: ImprintType
    style_signature: str = Field(..., description="Stylový podpis")
    emotional_context: List[EmotionalState] = Field(default_factory=list)
    behavioral_traits: List[BehavioralTrait] = Field(default_factory=list)


class ImprintCreate(ImprintBase):
    """Schéma pro vytvoření nového imprintu"""
    persona_id: UUID = Field(..., description="ID digitální osobnosti")
    training_data: List[Dict[str, str]] = Field(..., description="Tréninková data")
    
    # LoRA parametry
    lora_rank: int = Field(default=16, ge=1, le=256)
    lora_alpha: float = Field(default=32.0, ge=0.1, le=128.0)
    lora_dropout: float = Field(default=0.1, ge=0.0, le=0.5)
    target_modules: List[str] = Field(default_factory=lambda: ["q_proj", "v_proj"])
    
    # Tréninková nastavení
    training_epochs: int = Field(default=3, ge=1, le=20)
    learning_rate: float = Field(default=2e-4, ge=1e-6, le=1e-2)


class ImprintUpdate(BaseModel):
    """Schéma pro aktualizaci imprintu"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    status: Optional[ImprintStatus] = None
    emotional_context: Optional[List[EmotionalState]] = None
    behavioral_traits: Optional[List[BehavioralTrait]] = None
    user_feedback_score: Optional[float] = Field(None, ge=0.0, le=1.0)


class ImprintResponse(ImprintBase):
    """Schéma pro odpověď s imprintem"""
    id: str
    persona_id: UUID
    version: str
    status: ImprintStatus
    
    # LoRA parametry
    lora_rank: int
    lora_alpha: float
    lora_dropout: float
    target_modules: List[str]
    
    # Tréninková data info
    training_data_hash: Optional[str] = None
    training_samples_count: int
    training_epochs: int
    learning_rate: float
    
    # Výkonnostní metriky
    training_loss: Optional[float] = None
    validation_loss: Optional[float] = None
    perplexity: Optional[float] = None
    style_consistency_score: Optional[float] = None
    
    # Metadata
    model_path: Optional[str] = None
    config_path: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_used: Optional[datetime] = None
    
    # Statistiky použití
    usage_count: int
    success_rate: float
    user_feedback_score: Optional[float] = None

    class Config:
        from_attributes = True


class ImprintListResponse(BaseModel):
    """Schéma pro seznam imprintů"""
    imprints: List[ImprintResponse]
    total_count: int
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class ImprintTrainingConfigSchema(BaseModel):
    """Schéma pro konfiguraci tréninku"""
    base_model: str = Field(default="llama-7b")
    dataset_path: str
    output_dir: str
    
    # LoRA parametry
    lora_rank: int = Field(default=16, ge=1, le=256)
    lora_alpha: float = Field(default=32.0, ge=0.1, le=128.0)
    lora_dropout: float = Field(default=0.1, ge=0.0, le=0.5)
    target_modules: List[str] = Field(default_factory=lambda: ["q_proj", "v_proj"])
    
    # Tréninková nastavení
    num_epochs: int = Field(default=3, ge=1, le=20)
    learning_rate: float = Field(default=2e-4, ge=1e-6, le=1e-2)
    batch_size: int = Field(default=4, ge=1, le=32)
    gradient_accumulation_steps: int = Field(default=4, ge=1, le=16)
    warmup_steps: int = Field(default=100, ge=0, le=1000)
    
    # Validace a ukládání
    eval_steps: int = Field(default=500, ge=100, le=2000)
    save_steps: int = Field(default=1000, ge=100, le=5000)
    logging_steps: int = Field(default=100, ge=10, le=500)
    
    # Pokročilé nastavení
    fp16: bool = Field(default=True)
    gradient_checkpointing: bool = Field(default=True)
    dataloader_num_workers: int = Field(default=4, ge=0, le=16)


class ImprintActivationRequest(BaseModel):
    """Schéma pro aktivaci imprintu"""
    imprint_id: str = Field(..., description="ID imprintu k aktivaci")


class ImprintUsageUpdate(BaseModel):
    """Schéma pro aktualizaci statistik použití"""
    success: bool = Field(..., description="Byla interakce úspěšná?")
    user_feedback: Optional[float] = Field(None, ge=0.0, le=1.0, description="Hodnocení uživatele")
    response_quality: Optional[float] = Field(None, ge=0.0, le=1.0, description="Kvalita odpovědi")
    style_consistency: Optional[float] = Field(None, ge=0.0, le=1.0, description="Konzistence stylu")


class ImprintMetrics(BaseModel):
    """Schéma pro metriky imprintu"""
    usage_count: int
    success_rate: float
    average_user_feedback: Optional[float] = None
    style_consistency_score: Optional[float] = None
    last_used: Optional[datetime] = None
    performance_trend: str = Field(default="stable")  # improving, stable, declining


class ImprintAnalytics(BaseModel):
    """Schéma pro analytické údaje imprintu"""
    imprint_id: str
    persona_id: UUID
    
    # Základní metriky
    total_usage: int
    success_rate: float
    average_response_time: Optional[float] = None
    
    # Trendy v čase
    daily_usage: List[Dict[str, Any]] = Field(default_factory=list)
    weekly_performance: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Srovnání s jinými imprinty
    relative_performance: Optional[float] = None
    ranking_among_persona_imprints: Optional[int] = None
    
    # Doporučení
    optimization_suggestions: List[str] = Field(default_factory=list)
    retraining_recommended: bool = Field(default=False)


class ImprintSearchRequest(BaseModel):
    """Schéma pro vyhledávání imprintů"""
    persona_id: Optional[UUID] = None
    imprint_type: Optional[ImprintType] = None
    status: Optional[ImprintStatus] = None
    style_signature: Optional[str] = None
    emotional_context: Optional[List[EmotionalState]] = None
    behavioral_traits: Optional[List[BehavioralTrait]] = None
    
    # Filtry podle výkonu
    min_success_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    min_usage_count: Optional[int] = Field(None, ge=0)
    
    # Řazení a stránkování
    sort_by: str = Field(default="created_at")  # created_at, usage_count, success_rate
    sort_order: str = Field(default="desc")  # asc, desc
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class ImprintComparisonRequest(BaseModel):
    """Schéma pro porovnání imprintů"""
    imprint_ids: List[str] = Field(..., min_items=2, max_items=5)
    comparison_metrics: List[str] = Field(
        default_factory=lambda: ["success_rate", "usage_count", "user_feedback_score"]
    )


class ImprintComparisonResponse(BaseModel):
    """Schéma pro výsledek porovnání imprintů"""
    imprints: List[ImprintResponse]
    comparison_matrix: Dict[str, Dict[str, float]]
    best_performing: str = Field(..., description="ID nejlépe fungujícího imprintu")
    recommendations: List[str] = Field(default_factory=list)


class ImprintExportRequest(BaseModel):
    """Schéma pro export imprintu"""
    imprint_id: str
    export_format: str = Field(default="safetensors")  # safetensors, pytorch, onnx
    include_config: bool = Field(default=True)
    include_metrics: bool = Field(default=True)


class ImprintImportRequest(BaseModel):
    """Schéma pro import imprintu"""
    persona_id: UUID
    imprint_data: bytes = Field(..., description="Binární data imprintu")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    validate_compatibility: bool = Field(default=True)
