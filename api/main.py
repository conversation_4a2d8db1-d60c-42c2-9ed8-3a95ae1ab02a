"""
NESTOR Main API Service
Hlavní API služba pro komunikaci s frontendovými aplikacemi
"""

import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import structlog
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

# Temporarily comment out complex imports
# from models.base import create_tables
# from routers import (
#     auth_router,
#     users_router,
#     personalities_router,
#     health_router
# )

# Create simple health router directly in main
from fastapi import APIRouter
from datetime import datetime
from typing import Dict, Any

health_router = APIRouter(prefix="/health", tags=["health"])

@health_router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "nestor-api",
        "version": "1.0.0"
    }

# Konfigurace loggingu
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle management pro FastAPI aplikaci"""
    logger.info("Spouštění NESTOR Main API Service...")

    # Vytvoření databázových tabulek - temporarily disabled
    # try:
    #     create_tables()
    #     logger.info("Databázové tabulky úspěšně vytvořeny")
    # except Exception as e:
    #     logger.error("Chyba při vytváření databázových tabulek", error=str(e))

    logger.info("NESTOR Main API Service úspěšně spuštěn")

    yield

    logger.info("Ukončování NESTOR Main API Service...")

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="NESTOR Main API",
    description="Hlavní API služba pro komunikaci s frontendovými aplikacemi",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # V produkci nastavit konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Registrace routerů
app.include_router(health_router)
# app.include_router(auth_router)
# app.include_router(users_router)
# app.include_router(personalities_router)

# Prometheus metriky endpoint
@app.get("/metrics")
async def metrics():
    """Prometheus metriky"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
