# NESTOR - Návod pro obnovu vývojového prostředí

Tento návod vás provede kompletním procesem obnovy NESTOR vývojového prostředí po přeinstalaci PC.

## 📋 Obsah

1. [<PERSON><PERSON><PERSON><PERSON><PERSON> před přeinstalací](#1-příprava-před-přeinstalací)
2. [Po přeinstalaci PC](#2-po-přeinstalaci-pc)
3. [<PERSON>k<PERSON> obnova prostředí](#3-automatická-obnova-prostředí)
4. [Obnovení dat ze zálohy](#4-obnovení-dat-ze-zálohy)
5. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](#5-ov<PERSON><PERSON><PERSON><PERSON>-funkčnosti)
6. [Řešení problémů](#6-ř<PERSON><PERSON><PERSON><PERSON>-prob<PERSON><PERSON>ů)

---

## 1. Příprava před přeinstalací

### 1.1 Vytvoření zálohy dat

Před přeinstalací PC vytvořte zálohu všech dat:

```bash
# Spusťte zálohovací skript
./scripts/backup_volumes.sh
```

<PERSON><PERSON><PERSON><PERSON> bude uložena do `data/backups/backup_YYYYMMDD_HHMMSS/` a bude obsahovat:
- `nestor_postgres_data.tar.gz` - PostgreSQL data
- `nestor_redis_data.tar.gz` - Redis data  
- `database_dump.sql` - SQL dump databáze
- `backup_metadata.txt` - metadata zálohy

### 1.2 Uložení zálohy na externí úložiště

```bash
# Zkopírujte celý backup adresář na externí disk nebo cloud
cp -r data/backups/backup_YYYYMMDD_HHMMSS /path/to/external/storage/
```

### 1.3 Poznamenejte si důležité informace

- Verzi Podmanu: `podman --version`
- Verzi systému: `cat /etc/os-release`
- API klíče (pokud používáte) z `.env` souboru

---

## 2. Po přeinstalaci PC

### 2.1 Instalace základních nástrojů

```bash
# Fedora
sudo dnf install -y git curl wget

# Ubuntu/Debian
sudo apt update && sudo apt install -y git curl wget

# Arch Linux
sudo pacman -S git curl wget
```

### 2.2 Klonování projektu z GitHubu

```bash
# Klonování repozitáře
git clone https://github.com/your-username/nestor.git
cd nestor

# Nebo pokud používáte SSH
<NAME_EMAIL>:your-username/nestor.git
cd nestor
```

### 2.3 Nastavení oprávnění skriptů

```bash
# Nastavení spustitelných oprávnění
chmod +x setup_environment.sh
chmod +x scripts/*.sh
```

---

## 3. Automatická obnova prostředí

### 3.1 Spuštění setup skriptu

```bash
# Spuštění automatického setup skriptu
./setup_environment.sh
```

Skript automaticky:
- ✅ Detekuje vaši Linux distribuci
- ✅ Nainstaluje Podman (pokud není nainstalován)
- ✅ Nainstaluje podman-compose
- ✅ Zkontroluje závislosti (curl, git)
- ✅ Vytvoří `.env` soubor z template
- ✅ Vytvoří potřebné adresáře
- ✅ Spustí všechny služby pomocí Podman Compose
- ✅ Inicializuje databázi
- ✅ Načte ukázková data
- ✅ Zkontroluje zdraví všech služeb

### 3.2 Úprava .env souboru (volitelné)

Pokud používáte API klíče pro LLM služby:

```bash
# Editace .env souboru
nano .env

# Přidejte vaše API klíče:
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
```

Po úpravě restartujte služby:

```bash
podman-compose -f podman-compose.yml restart
```

---

## 4. Obnovení dat ze zálohy

### 4.1 Zkopírování zálohy

```bash
# Zkopírujte zálohu z externího úložiště
cp -r /path/to/external/storage/backup_YYYYMMDD_HHMMSS data/backups/
```

### 4.2 Spuštění obnovy

```bash
# Obnovení dat ze zálohy
./scripts/restore_volumes.sh data/backups/backup_YYYYMMDD_HHMMSS
```

⚠️ **VAROVÁNÍ**: Tato operace odstraní všechna současná data!

Skript se zeptá na potvrzení před pokračováním.

### 4.3 Co skript obnovy dělá

1. Zastaví všechny služby
2. Odstraní existující volumes
3. Vytvoří nové volumes
4. Obnoví data z tar archivů
5. Obnoví databázi z SQL dump
6. Spustí služby
7. Ověří úspěšnost obnovy

---

## 5. Ověření funkčnosti

### 5.1 Kontrola stavu služeb

```bash
# Zobrazení stavu všech služeb
podman-compose -f podman-compose.yml ps

# Zobrazení logů
podman-compose -f podman-compose.yml logs -f
```

### 5.2 Testování API endpointů

```bash
# Test hlavního API
curl http://localhost:8000/health

# Test Memory Service
curl http://localhost:8001/health

# Test RAG Service  
curl http://localhost:8002/health

# Test LLM Interface
curl http://localhost:8003/health

# Test Tokenization Service
curl http://localhost:8004/health
```

### 5.3 Kontrola databáze

```bash
# Připojení k databázi
podman exec -it nestor-postgres psql -U nestor -d nestor

# Kontrola tabulek
\dt

# Kontrola dat
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM personalities;
SELECT COUNT(*) FROM memories;

# Ukončení
\q
```

---

## 6. Řešení problémů

### 6.1 Podman není nainstalován

```bash
# Fedora
sudo dnf install -y podman podman-compose

# Ubuntu/Debian  
sudo apt install -y podman
pip3 install --user podman-compose

# Arch Linux
sudo pacman -S podman podman-compose
```

### 6.2 Služby se nespouštějí

```bash
# Kontrola logů konkrétní služby
podman logs nestor-postgres
podman logs nestor-redis
podman logs nestor-api

# Restart všech služeb
podman-compose -f podman-compose.yml down
podman-compose -f podman-compose.yml up -d
```

### 6.3 Databáze není dostupná

```bash
# Kontrola PostgreSQL kontejneru
podman exec nestor-postgres pg_isready -U nestor -d nestor

# Restart PostgreSQL
podman-compose -f podman-compose.yml restart postgres

# Reinicializace databáze
podman exec -i nestor-postgres psql -U nestor -d nestor < data/migrations/01_init.sql
```

### 6.4 Porty jsou obsazené

```bash
# Kontrola obsazených portů
sudo netstat -tulpn | grep :8000
sudo netstat -tulpn | grep :5432

# Zastavení konfliktních služeb
sudo systemctl stop postgresql
sudo systemctl stop redis
```

### 6.5 Nedostatek místa na disku

```bash
# Vyčištění nepoužívaných Podman objektů
podman system prune -a

# Kontrola velikosti volumes
podman volume ls
podman system df
```

---

## 📞 Podpora

Pokud narazíte na problémy:

1. Zkontrolujte logy: `podman-compose logs -f`
2. Ověřte stav služeb: `podman-compose ps`
3. Restartujte problematickou službu
4. V krajním případě spusťte celý setup znovu

---

## 🔄 Pravidelná údržba

### Týdenní záloha

```bash
# Vytvořte pravidelnou zálohu
./scripts/backup_volumes.sh
```

### Aktualizace obrazů

```bash
# Stažení nejnovějších obrazů
podman-compose -f podman-compose.yml pull

# Restart s novými obrazy
podman-compose -f podman-compose.yml up -d
```

### Vyčištění starých záloh

```bash
# Odstranění záloh starších než 30 dní
find data/backups -name "backup_*" -type d -mtime +30 -exec rm -rf {} \;
```

---

**✅ Po dokončení tohoto návodu byste měli mít plně funkční NESTOR vývojové prostředí!**
