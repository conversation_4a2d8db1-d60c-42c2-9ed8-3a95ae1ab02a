"""
Reconstruction Engine - rekonstruuje odpověď na základě imprintů a vektorových pamětí

Tento modul implementuje centrální logiku pro rekonstrukci odpovědí digitální osobnosti
na základě naučených vzorců, em<PERSON><PERSON><PERSON><PERSON><PERSON> stavů a kontextových pamětí.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
import structlog
import asyncio

from .affective_profile import AffectiveProfile, EmotionalState
from memory.core.memory_types import Memory, MemoryType, MemoryQuery

logger = structlog.get_logger()


class ReconstructionContext(BaseModel):
    """Kontext pro rekonstrukci odpovědi"""
    persona_id: str
    input_text: str
    conversation_history: List[Dict[str, str]] = Field(default_factory=list)
    current_emotion: Optional[EmotionalState] = None
    active_memories: List[Memory] = Field(default_factory=list)
    style_preferences: Dict[str, Any] = Field(default_factory=dict)
    temporal_context: Optional[datetime] = None
    user_context: Dict[str, Any] = Field(default_factory=dict)


class ReconstructionResult(BaseModel):
    """Výsledek rekonstrukce odpovědi"""
    reconstructed_text: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    emotional_coloring: EmotionalState
    style_signature: str
    used_memories: List[str] = Field(default_factory=list)
    affective_modulation: Dict[str, float] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ReconstructionEngine:
    """Engine pro rekonstrukci odpovědí na základě persona imprintů"""
    
    def __init__(self, memory_manager=None, llm_service=None):
        self.memory_manager = memory_manager
        self.llm_service = llm_service
        self.reconstruction_cache = {}
        
    async def reconstruct_response(
        self,
        context: ReconstructionContext,
        affective_profile: AffectiveProfile,
        imprint_data: Optional[Dict[str, Any]] = None
    ) -> ReconstructionResult:
        """Hlavní metoda pro rekonstrukci odpovědi"""
        
        try:
            logger.info("Zahajuji rekonstrukci odpovědi", 
                       persona_id=context.persona_id,
                       input_length=len(context.input_text))
            
            # 1. Analýza vstupního kontextu
            context_analysis = await self._analyze_input_context(context)
            
            # 2. Vyhledání relevantních pamětí
            relevant_memories = await self._retrieve_relevant_memories(context, affective_profile)
            
            # 3. Aplikace afektivní modulace
            affective_modulation = affective_profile.calculate_response_modulation(context.input_text)
            
            # 4. Rekonstrukce na základě imprintů
            base_response = await self._reconstruct_from_imprints(
                context, affective_profile, relevant_memories, imprint_data
            )
            
            # 5. Stylová úprava odpovědi
            styled_response = await self._apply_style_modulation(
                base_response, affective_profile, affective_modulation
            )
            
            # 6. Finální validace a úpravy
            final_response = await self._finalize_response(styled_response, context)
            
            # 7. Výpočet confidence score
            confidence = self._calculate_confidence(
                context, relevant_memories, affective_modulation
            )
            
            result = ReconstructionResult(
                reconstructed_text=final_response,
                confidence_score=confidence,
                emotional_coloring=affective_profile.primary_emotion,
                style_signature=affective_profile.get_style_signature(),
                used_memories=[mem.id for mem in relevant_memories],
                affective_modulation=affective_modulation,
                metadata={
                    "context_analysis": context_analysis,
                    "reconstruction_timestamp": datetime.utcnow().isoformat(),
                    "engine_version": "1.0"
                }
            )
            
            logger.info("Rekonstrukce dokončena", 
                       confidence=confidence,
                       style_signature=result.style_signature)
            
            return result
            
        except Exception as e:
            logger.error("Chyba při rekonstrukci odpovědi", error=str(e))
            # Fallback na základní odpověď
            return await self._create_fallback_response(context, affective_profile)
    
    async def _analyze_input_context(self, context: ReconstructionContext) -> Dict[str, Any]:
        """Analyzuje vstupní kontext pro lepší porozumění"""
        analysis = {
            "input_length": len(context.input_text),
            "conversation_length": len(context.conversation_history),
            "has_emotional_markers": self._detect_emotional_markers(context.input_text),
            "question_type": self._classify_question_type(context.input_text),
            "urgency_level": self._assess_urgency(context.input_text),
            "personal_references": self._detect_personal_references(context.input_text)
        }
        return analysis
    
    async def _retrieve_relevant_memories(
        self, 
        context: ReconstructionContext, 
        affective_profile: AffectiveProfile
    ) -> List[Memory]:
        """Vyhledá relevantní paměti pro rekonstrukci"""
        if not self.memory_manager:
            return []
        
        # Vytvoření dotazu pro vyhledání pamětí
        memory_query = MemoryQuery(
            query_text=context.input_text,
            personality_id=context.persona_id,
            top_k=5,
            similarity_threshold=0.7,
            memory_types=[MemoryType.EPISODIC, MemoryType.SEMANTIC, MemoryType.EMOTIONAL]
        )
        
        try:
            search_results = await self.memory_manager.search_memories(memory_query)
            return [result.memory for result in search_results]
        except Exception as e:
            logger.warning("Chyba při vyhledávání pamětí", error=str(e))
            return []
    
    async def _reconstruct_from_imprints(
        self,
        context: ReconstructionContext,
        affective_profile: AffectiveProfile,
        memories: List[Memory],
        imprint_data: Optional[Dict[str, Any]]
    ) -> str:
        """Rekonstruuje odpověď na základě LoRA imprintů"""
        
        # Sestavení promptu s kontextem
        prompt_parts = []
        
        # Základní kontext osobnosti
        prompt_parts.append(f"Osobnost: {affective_profile.name}")
        prompt_parts.append(f"Styl: {affective_profile.get_style_signature()}")
        prompt_parts.append(f"Emoční stav: {affective_profile.primary_emotion.value}")
        
        # Relevantní paměti
        if memories:
            memory_context = "\n".join([f"- {mem.content}" for mem in memories[:3]])
            prompt_parts.append(f"Relevantní vzpomínky:\n{memory_context}")
        
        # Konverzační historie
        if context.conversation_history:
            history = "\n".join([
                f"{msg['role']}: {msg['content']}" 
                for msg in context.conversation_history[-3:]
            ])
            prompt_parts.append(f"Nedávná konverzace:\n{history}")
        
        # Aktuální vstup
        prompt_parts.append(f"Uživatel: {context.input_text}")
        prompt_parts.append("Odpověď:")
        
        full_prompt = "\n\n".join(prompt_parts)
        
        # Generování odpovědi pomocí LLM
        if self.llm_service:
            try:
                response = await self.llm_service.generate(
                    prompt=full_prompt,
                    max_tokens=150,
                    temperature=0.7 + (affective_profile.current_affective_state.arousal * 0.2)
                )
                return response.get("text", "Omlouvám se, nemohu odpovědět.")
            except Exception as e:
                logger.warning("Chyba při generování LLM odpovědi", error=str(e))
        
        # Fallback na template odpověď
        return self._generate_template_response(context, affective_profile)
    
    async def _apply_style_modulation(
        self,
        base_response: str,
        affective_profile: AffectiveProfile,
        modulation: Dict[str, float]
    ) -> str:
        """Aplikuje stylovou modulaci na základě afektivního profilu"""
        
        # Jednoduché stylové úpravy na základě emočního stavu
        response = base_response
        
        # Modulace na základě valence
        if modulation.get("valence_bias", 0) > 0.3:
            response = self._add_positive_markers(response)
        elif modulation.get("valence_bias", 0) < -0.3:
            response = self._add_melancholic_markers(response)
        
        # Modulace na základě arousal
        if modulation.get("arousal_level", 0) > 0.5:
            response = self._add_energetic_markers(response)
        
        # Modulace na základě dominance
        if modulation.get("dominance_level", 0) < -0.3:
            response = self._add_submissive_markers(response)
        
        return response
    
    def _detect_emotional_markers(self, text: str) -> bool:
        """Detekuje emoční markery v textu"""
        emotional_words = ["cítím", "emoce", "láska", "strach", "radost", "smutek", "hněv"]
        return any(word in text.lower() for word in emotional_words)
    
    def _classify_question_type(self, text: str) -> str:
        """Klasifikuje typ otázky"""
        if "?" in text:
            if any(word in text.lower() for word in ["co", "jak", "kdy", "kde", "proč"]):
                return "informational"
            else:
                return "yes_no"
        return "statement"
    
    def _assess_urgency(self, text: str) -> float:
        """Hodnotí naléhavost zprávy"""
        urgent_markers = ["rychle", "ihned", "naléhavé", "pomoc", "problém"]
        return min(1.0, sum(1 for marker in urgent_markers if marker in text.lower()) * 0.3)
    
    def _detect_personal_references(self, text: str) -> bool:
        """Detekuje osobní odkazy v textu"""
        personal_markers = ["já", "mě", "můj", "moje", "moje", "ty", "tvůj", "tvoje"]
        return any(marker in text.lower() for marker in personal_markers)
    
    def _generate_template_response(
        self, 
        context: ReconstructionContext, 
        affective_profile: AffectiveProfile
    ) -> str:
        """Generuje template odpověď jako fallback"""
        templates = {
            EmotionalState.JOY: "To zní skvěle! Jsem ráda, že se o to dělíš.",
            EmotionalState.SADNESS: "Rozumím ti... někdy je těžké najít správná slova.",
            EmotionalState.NEUTRAL: "Zajímavé, co si o tom myslíš?",
            EmotionalState.CONTEMPLATIVE: "Hmm, to je něco, nad čím stojí za to přemýšlet...",
            EmotionalState.PLAYFUL: "Hehe, to je zábavné! Co ještě máš na mysli?"
        }
        
        return templates.get(
            affective_profile.primary_emotion,
            "Děkuji, že se se mnou sdílíš své myšlenky."
        )
    
    def _add_positive_markers(self, text: str) -> str:
        """Přidává pozitivní jazykové markery"""
        if not text.endswith(("!", ".", "?")):
            text += "!"
        return text
    
    def _add_melancholic_markers(self, text: str) -> str:
        """Přidává melancholické markery"""
        return text + "..."
    
    def _add_energetic_markers(self, text: str) -> str:
        """Přidává energické markery"""
        return text.replace(".", "!")
    
    def _add_submissive_markers(self, text: str) -> str:
        """Přidává submisivní markery"""
        if not text.lower().startswith(("možná", "myslím", "zdá se")):
            text = "Myslím, že " + text.lower()
        return text
    
    def _calculate_confidence(
        self,
        context: ReconstructionContext,
        memories: List[Memory],
        modulation: Dict[str, float]
    ) -> float:
        """Vypočítá confidence score pro rekonstrukci"""
        base_confidence = 0.5
        
        # Bonus za relevantní paměti
        if memories:
            base_confidence += min(0.3, len(memories) * 0.1)
        
        # Bonus za konverzační kontext
        if context.conversation_history:
            base_confidence += min(0.2, len(context.conversation_history) * 0.05)
        
        # Malus za vysokou emoční intenzitu (nejistota)
        emotional_intensity = modulation.get("emotional_intensity", 0.5)
        if emotional_intensity > 0.8:
            base_confidence -= 0.1
        
        return min(1.0, max(0.1, base_confidence))
    
    async def _finalize_response(self, response: str, context: ReconstructionContext) -> str:
        """Finalizuje odpověď před vrácením"""
        # Základní čištění a formátování
        response = response.strip()
        
        # Zajištění, že odpověď není příliš dlouhá
        if len(response) > 500:
            response = response[:497] + "..."
        
        return response
    
    async def _create_fallback_response(
        self, 
        context: ReconstructionContext, 
        affective_profile: AffectiveProfile
    ) -> ReconstructionResult:
        """Vytvoří fallback odpověď v případě chyby"""
        fallback_text = "Omlouvám se, momentálně mám problém s odpovědí. Můžeš to zkusit znovu?"
        
        return ReconstructionResult(
            reconstructed_text=fallback_text,
            confidence_score=0.1,
            emotional_coloring=EmotionalState.NEUTRAL,
            style_signature="fallback",
            metadata={"fallback": True, "error_recovery": True}
        )
