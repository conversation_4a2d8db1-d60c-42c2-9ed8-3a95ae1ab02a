"""
Affective Profile - definuje emoční a behaviorální parametry digitální osobnosti

Tento modul implementuje biologicky inspirovaný systém pro modelování
emočních stavů a behaviorálních vzorců digitálních osobností.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
import uuid


class EmotionalState(str, Enum):
    """Základní emoční stavy podle psychologických modelů"""
    JOY = "joy"                    # Radost
    SADNESS = "sadness"            # Smutek
    ANGER = "anger"                # Hněv
    FEAR = "fear"                  # Strach
    SURPRISE = "surprise"          # Překvapení
    DISGUST = "disgust"            # Odpor
    TRUST = "trust"                # Důvěra
    ANTICIPATION = "anticipation"  # Očekávání
    NEUTRAL = "neutral"            # Neutrální
    CONTEMPLATIVE = "contemplative" # Zamyšlený
    PLAYFUL = "playful"            # Hravý
    MELANCHOLIC = "melancholic"    # Melancholický


class BehavioralTrait(str, Enum):
    """Behaviorální rysy osobnosti"""
    SUBMISSIVE = "submissive"      # Submisivní
    DOMINANT = "dominant"          # Dominantní
    DREAMY = "dreamy"              # Snivý
    PRACTICAL = "practical"        # Praktický
    SENSUAL = "sensual"            # Smyslný
    INTELLECTUAL = "intellectual"  # Intelektuální
    OBEDIENT = "obedient"          # Poslušný
    REBELLIOUS = "rebellious"      # Rebelský
    CARING = "caring"              # Pečující
    INDEPENDENT = "independent"    # Nezávislý
    ROMANTIC = "romantic"          # Romantický
    ANALYTICAL = "analytical"      # Analytický


class AffectiveVector(BaseModel):
    """Vektorová reprezentace emočního stavu"""
    valence: float = Field(default=0.0, ge=-1.0, le=1.0, description="Pozitivita/negativita (-1 až 1)")
    arousal: float = Field(default=0.0, ge=-1.0, le=1.0, description="Aktivace/uklidnění (-1 až 1)")
    dominance: float = Field(default=0.0, ge=-1.0, le=1.0, description="Kontrola/submise (-1 až 1)")
    intensity: float = Field(default=0.5, ge=0.0, le=1.0, description="Intenzita emoce (0 až 1)")


class PersonalityDimensions(BaseModel):
    """Pětifaktorový model osobnosti (Big Five)"""
    openness: float = Field(default=0.5, ge=0.0, le=1.0, description="Otevřenost zkušenostem")
    conscientiousness: float = Field(default=0.5, ge=0.0, le=1.0, description="Svědomitost")
    extraversion: float = Field(default=0.5, ge=0.0, le=1.0, description="Extraverze")
    agreeableness: float = Field(default=0.5, ge=0.0, le=1.0, description="Přívětivost")
    neuroticism: float = Field(default=0.5, ge=0.0, le=1.0, description="Neuroticismus")


class AffectiveProfile(BaseModel):
    """Kompletní afektivní profil digitální osobnosti"""
    
    # Identifikace
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    persona_id: str = Field(..., description="ID digitální osobnosti")
    name: str = Field(..., description="Název profilu")
    
    # Základní emoční charakteristiky
    primary_emotion: EmotionalState = Field(default=EmotionalState.NEUTRAL)
    secondary_emotions: List[EmotionalState] = Field(default_factory=list)
    emotional_stability: float = Field(default=0.5, ge=0.0, le=1.0, description="Emoční stabilita")
    emotional_range: float = Field(default=0.5, ge=0.0, le=1.0, description="Rozsah emočních reakcí")
    
    # Behaviorální rysy
    primary_traits: List[BehavioralTrait] = Field(default_factory=list)
    trait_weights: Dict[BehavioralTrait, float] = Field(default_factory=dict)
    
    # Vektorové reprezentace
    current_affective_state: AffectiveVector = Field(default_factory=AffectiveVector)
    baseline_affective_state: AffectiveVector = Field(default_factory=AffectiveVector)
    personality_dimensions: PersonalityDimensions = Field(default_factory=PersonalityDimensions)
    
    # Stylové parametry
    communication_style: str = Field(default="neutral", description="Styl komunikace")
    response_patterns: Dict[str, Any] = Field(default_factory=dict, description="Vzorce odpovědí")
    linguistic_markers: List[str] = Field(default_factory=list, description="Jazykové markery")
    
    # Adaptivní parametry
    learning_rate: float = Field(default=0.1, ge=0.0, le=1.0, description="Rychlost učení")
    adaptation_threshold: float = Field(default=0.3, ge=0.0, le=1.0, description="Práh adaptace")
    memory_decay: float = Field(default=0.05, ge=0.0, le=1.0, description="Rychlost zapomínání")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(default="1.0")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def update_emotional_state(self, new_emotion: EmotionalState, intensity: float = 0.5) -> None:
        """Aktualizuje emoční stav s ohledem na stabilitu"""
        if intensity > self.adaptation_threshold:
            # Silná emoce ovlivní současný stav
            self.primary_emotion = new_emotion
            self.current_affective_state.intensity = min(
                1.0, 
                self.current_affective_state.intensity + intensity * (1 - self.emotional_stability)
            )
        self.updated_at = datetime.utcnow()
    
    def get_style_signature(self) -> str:
        """Vrací stylový podpis pro LoRA trénink"""
        traits_str = "-".join([trait.value for trait in self.primary_traits[:3]])
        emotion_str = self.primary_emotion.value
        return f"{traits_str}-{emotion_str}"
    
    def calculate_response_modulation(self, context: str) -> Dict[str, float]:
        """Vypočítá modulaci odpovědi na základě afektivního stavu"""
        return {
            "emotional_intensity": self.current_affective_state.intensity,
            "valence_bias": self.current_affective_state.valence,
            "arousal_level": self.current_affective_state.arousal,
            "dominance_level": self.current_affective_state.dominance,
            "trait_influence": sum(self.trait_weights.values()) / len(self.trait_weights) if self.trait_weights else 0.5
        }
    
    def decay_emotional_state(self) -> None:
        """Aplikuje přirozený rozpad emočního stavu směrem k baseline"""
        decay_factor = self.memory_decay
        
        # Postupný návrat k baseline stavu
        self.current_affective_state.valence += (
            self.baseline_affective_state.valence - self.current_affective_state.valence
        ) * decay_factor
        
        self.current_affective_state.arousal += (
            self.baseline_affective_state.arousal - self.current_affective_state.arousal
        ) * decay_factor
        
        self.current_affective_state.intensity *= (1 - decay_factor)
        
        self.updated_at = datetime.utcnow()
