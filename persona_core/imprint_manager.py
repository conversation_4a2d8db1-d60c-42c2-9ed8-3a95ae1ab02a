"""
Imprint Manager - spra<PERSON><PERSON> imprinty, v<PERSON><PERSON><PERSON><PERSON> stylu, emocí a připojených dat

Tento modul implementuje správu LoRA (Low-Rank Adaptation) imprintů pro
personalizaci jazykových modelů podle specifických stylů a charakteristik
digitálních osobností.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import structlog
import json
import hashlib

from .affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait

logger = structlog.get_logger()


class ImprintType(str, Enum):
    """Typy LoRA imprintů"""
    STYLE = "style"                # Stylový imprint
    EMOTIONAL = "emotional"        # Emoční imprint
    BEHAVIORAL = "behavioral"      # Behaviorální imprint
    CONVERSATIONAL = "conversational"  # Konverzační imprint
    DOMAIN_SPECIFIC = "domain_specific"  # Doménově specifický
    COMPOSITE = "composite"        # Kompozitní (kombinace více typů)


class ImprintStatus(str, Enum):
    """Stavy LoRA imprintu"""
    TRAINING = "training"          # Probíhá trénink
    READY = "ready"               # Připraven k použití
    ACTIVE = "active"             # Aktivně používán
    DEPRECATED = "deprecated"      # Zastaralý
    FAILED = "failed"             # Trénink selhal


class PersonaImprint(BaseModel):
    """LoRA imprint pro digitální osobnost"""
    
    # Identifikace
    id: str = Field(..., description="Unikátní ID imprintu")
    persona_id: str = Field(..., description="ID osobnosti")
    name: str = Field(..., description="Název imprintu")
    version: str = Field(default="1.0", description="Verze imprintu")
    
    # Typ a konfigurace
    imprint_type: ImprintType = Field(..., description="Typ imprintu")
    status: ImprintStatus = Field(default=ImprintStatus.TRAINING)
    
    # Stylové parametry
    style_signature: str = Field(..., description="Stylový podpis")
    emotional_context: List[EmotionalState] = Field(default_factory=list)
    behavioral_traits: List[BehavioralTrait] = Field(default_factory=list)
    
    # LoRA specifické parametry
    lora_rank: int = Field(default=16, ge=1, le=256, description="LoRA rank")
    lora_alpha: float = Field(default=32.0, ge=0.1, le=128.0, description="LoRA alpha")
    lora_dropout: float = Field(default=0.1, ge=0.0, le=0.5, description="LoRA dropout")
    target_modules: List[str] = Field(default_factory=lambda: ["q_proj", "v_proj"])
    
    # Tréninková data
    training_data_hash: Optional[str] = None
    training_samples_count: int = Field(default=0)
    training_epochs: int = Field(default=3)
    learning_rate: float = Field(default=2e-4)
    
    # Výkonnostní metriky
    training_loss: Optional[float] = None
    validation_loss: Optional[float] = None
    perplexity: Optional[float] = None
    style_consistency_score: Optional[float] = None
    
    # Metadata
    model_path: Optional[str] = None
    config_path: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None
    
    # Použití a statistiky
    usage_count: int = Field(default=0)
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    user_feedback_score: Optional[float] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ImprintTrainingConfig(BaseModel):
    """Konfigurace pro trénink LoRA imprintu"""
    
    # Základní parametry
    base_model: str = Field(default="llama-7b", description="Základní model")
    dataset_path: str = Field(..., description="Cesta k tréninkovým datům")
    output_dir: str = Field(..., description="Výstupní adresář")
    
    # LoRA parametry
    lora_rank: int = Field(default=16)
    lora_alpha: float = Field(default=32.0)
    lora_dropout: float = Field(default=0.1)
    target_modules: List[str] = Field(default_factory=lambda: ["q_proj", "v_proj"])
    
    # Tréninková nastavení
    num_epochs: int = Field(default=3, ge=1, le=20)
    learning_rate: float = Field(default=2e-4, ge=1e-6, le=1e-2)
    batch_size: int = Field(default=4, ge=1, le=32)
    gradient_accumulation_steps: int = Field(default=4)
    warmup_steps: int = Field(default=100)
    
    # Validace a ukládání
    eval_steps: int = Field(default=500)
    save_steps: int = Field(default=1000)
    logging_steps: int = Field(default=100)
    
    # Pokročilé nastavení
    fp16: bool = Field(default=True)
    gradient_checkpointing: bool = Field(default=True)
    dataloader_num_workers: int = Field(default=4)


class ImprintManager:
    """Správce LoRA imprintů pro digitální osobnosti"""
    
    def __init__(self, storage_backend=None, training_backend=None):
        self.storage_backend = storage_backend
        self.training_backend = training_backend
        self.active_imprints: Dict[str, PersonaImprint] = {}
        self.imprint_cache: Dict[str, Any] = {}
        
    async def create_imprint(
        self,
        persona_id: str,
        affective_profile: AffectiveProfile,
        training_data: List[Dict[str, str]],
        imprint_type: ImprintType = ImprintType.STYLE,
        config: Optional[ImprintTrainingConfig] = None
    ) -> PersonaImprint:
        """Vytvoří nový LoRA imprint pro osobnost"""
        
        # Generování ID a stylového podpisu
        imprint_id = self._generate_imprint_id(persona_id, affective_profile)
        style_signature = affective_profile.get_style_signature()
        
        # Vytvoření imprintu
        imprint = PersonaImprint(
            id=imprint_id,
            persona_id=persona_id,
            name=f"{affective_profile.name}_{style_signature}",
            imprint_type=imprint_type,
            style_signature=style_signature,
            emotional_context=[affective_profile.primary_emotion] + affective_profile.secondary_emotions,
            behavioral_traits=affective_profile.primary_traits,
            training_data_hash=self._hash_training_data(training_data),
            training_samples_count=len(training_data)
        )
        
        logger.info("Vytváření nového LoRA imprintu",
                   imprint_id=imprint_id,
                   persona_id=persona_id,
                   style_signature=style_signature)
        
        # Spuštění tréninku
        if self.training_backend:
            try:
                training_config = config or self._create_default_config(imprint)
                await self._start_training(imprint, training_data, training_config)
            except Exception as e:
                logger.error("Chyba při spouštění tréninku", error=str(e))
                imprint.status = ImprintStatus.FAILED
        
        # Uložení imprintu
        await self._store_imprint(imprint)
        
        return imprint
    
    async def load_imprint(self, imprint_id: str) -> Optional[PersonaImprint]:
        """Načte imprint podle ID"""
        
        # Kontrola cache
        if imprint_id in self.imprint_cache:
            return self.imprint_cache[imprint_id]
        
        # Načtení z úložiště
        if self.storage_backend:
            try:
                imprint = await self.storage_backend.load_imprint(imprint_id)
                if imprint:
                    self.imprint_cache[imprint_id] = imprint
                return imprint
            except Exception as e:
                logger.error("Chyba při načítání imprintu", error=str(e))
        
        return None
    
    async def activate_imprint(self, imprint_id: str) -> bool:
        """Aktivuje imprint pro použití"""
        
        imprint = await self.load_imprint(imprint_id)
        if not imprint or imprint.status != ImprintStatus.READY:
            logger.warning("Nelze aktivovat imprint", 
                          imprint_id=imprint_id,
                          status=imprint.status if imprint else "not_found")
            return False
        
        # Aktivace
        imprint.status = ImprintStatus.ACTIVE
        imprint.last_used = datetime.utcnow()
        self.active_imprints[imprint.persona_id] = imprint
        
        await self._store_imprint(imprint)
        
        logger.info("Imprint aktivován", imprint_id=imprint_id)
        return True
    
    async def deactivate_imprint(self, persona_id: str) -> bool:
        """Deaktivuje aktivní imprint pro osobnost"""
        
        if persona_id in self.active_imprints:
            imprint = self.active_imprints[persona_id]
            imprint.status = ImprintStatus.READY
            await self._store_imprint(imprint)
            del self.active_imprints[persona_id]
            
            logger.info("Imprint deaktivován", persona_id=persona_id)
            return True
        
        return False
    
    def get_active_imprint(self, persona_id: str) -> Optional[PersonaImprint]:
        """Vrátí aktivní imprint pro osobnost"""
        return self.active_imprints.get(persona_id)
    
    async def list_imprints(
        self,
        persona_id: Optional[str] = None,
        imprint_type: Optional[ImprintType] = None,
        status: Optional[ImprintStatus] = None
    ) -> List[PersonaImprint]:
        """Vrátí seznam imprintů podle filtrů"""
        
        if not self.storage_backend:
            return list(self.active_imprints.values())
        
        try:
            return await self.storage_backend.list_imprints(
                persona_id=persona_id,
                imprint_type=imprint_type,
                status=status
            )
        except Exception as e:
            logger.error("Chyba při načítání seznamu imprintů", error=str(e))
            return []
    
    async def update_imprint_metrics(
        self,
        imprint_id: str,
        success: bool,
        user_feedback: Optional[float] = None
    ) -> bool:
        """Aktualizuje metriky imprintu na základě použití"""
        
        imprint = await self.load_imprint(imprint_id)
        if not imprint:
            return False
        
        # Aktualizace statistik
        imprint.usage_count += 1
        
        # Aktualizace success rate
        current_successes = imprint.success_rate * (imprint.usage_count - 1)
        new_successes = current_successes + (1 if success else 0)
        imprint.success_rate = new_successes / imprint.usage_count
        
        # Aktualizace user feedback
        if user_feedback is not None:
            if imprint.user_feedback_score is None:
                imprint.user_feedback_score = user_feedback
            else:
                # Exponenciální průměr
                alpha = 0.1
                imprint.user_feedback_score = (
                    alpha * user_feedback + (1 - alpha) * imprint.user_feedback_score
                )
        
        imprint.last_used = datetime.utcnow()
        imprint.updated_at = datetime.utcnow()
        
        await self._store_imprint(imprint)
        
        logger.debug("Metriky imprintu aktualizovány",
                    imprint_id=imprint_id,
                    success_rate=imprint.success_rate,
                    usage_count=imprint.usage_count)
        
        return True
    
    async def delete_imprint(self, imprint_id: str) -> bool:
        """Smaže imprint"""
        
        # Deaktivace pokud je aktivní
        imprint = await self.load_imprint(imprint_id)
        if imprint and imprint.persona_id in self.active_imprints:
            await self.deactivate_imprint(imprint.persona_id)
        
        # Smazání z úložiště
        if self.storage_backend:
            try:
                await self.storage_backend.delete_imprint(imprint_id)
                
                # Smazání z cache
                if imprint_id in self.imprint_cache:
                    del self.imprint_cache[imprint_id]
                
                logger.info("Imprint smazán", imprint_id=imprint_id)
                return True
            except Exception as e:
                logger.error("Chyba při mazání imprintu", error=str(e))
        
        return False
    
    def _generate_imprint_id(self, persona_id: str, affective_profile: AffectiveProfile) -> str:
        """Generuje unikátní ID pro imprint"""
        timestamp = datetime.utcnow().isoformat()
        style_sig = affective_profile.get_style_signature()
        
        hash_input = f"{persona_id}_{style_sig}_{timestamp}"
        hash_digest = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        
        return f"imprint_{persona_id}_{hash_digest}"
    
    def _hash_training_data(self, training_data: List[Dict[str, str]]) -> str:
        """Vytvoří hash tréninkovách dat"""
        data_str = json.dumps(training_data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    def _create_default_config(self, imprint: PersonaImprint) -> ImprintTrainingConfig:
        """Vytvoří výchozí konfiguraci pro trénink"""
        return ImprintTrainingConfig(
            dataset_path=f"/tmp/training_data_{imprint.id}.jsonl",
            output_dir=f"/models/imprints/{imprint.id}",
            lora_rank=imprint.lora_rank,
            lora_alpha=imprint.lora_alpha,
            lora_dropout=imprint.lora_dropout,
            target_modules=imprint.target_modules
        )
    
    async def _start_training(
        self,
        imprint: PersonaImprint,
        training_data: List[Dict[str, str]],
        config: ImprintTrainingConfig
    ):
        """Spustí trénink LoRA imprintu"""
        
        if not self.training_backend:
            logger.warning("Training backend není dostupný")
            return
        
        try:
            # Příprava dat
            await self._prepare_training_data(training_data, config.dataset_path)
            
            # Spuštění tréninku
            training_result = await self.training_backend.train_lora(imprint, config)
            
            # Aktualizace imprintu s výsledky
            if training_result.get("success"):
                imprint.status = ImprintStatus.READY
                imprint.model_path = training_result.get("model_path")
                imprint.training_loss = training_result.get("training_loss")
                imprint.validation_loss = training_result.get("validation_loss")
                imprint.perplexity = training_result.get("perplexity")
            else:
                imprint.status = ImprintStatus.FAILED
                
        except Exception as e:
            logger.error("Chyba při tréninku imprintu", error=str(e))
            imprint.status = ImprintStatus.FAILED
    
    async def _prepare_training_data(
        self,
        training_data: List[Dict[str, str]],
        output_path: str
    ):
        """Připraví tréninková data ve formátu pro LoRA"""
        
        # Konverze do JSONL formátu
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in training_data:
                json_line = json.dumps(item, ensure_ascii=False)
                f.write(json_line + '\n')
        
        logger.debug("Tréninková data připravena", 
                    path=output_path,
                    samples=len(training_data))
    
    async def _store_imprint(self, imprint: PersonaImprint):
        """Uloží imprint do úložiště"""
        
        if self.storage_backend:
            try:
                await self.storage_backend.store_imprint(imprint)
                # Aktualizace cache
                self.imprint_cache[imprint.id] = imprint
            except Exception as e:
                logger.error("Chyba při ukládání imprintu", error=str(e))
        else:
            # Fallback - uložení do cache
            self.imprint_cache[imprint.id] = imprint
