"""
NESTOR Persona Core Module

Centrální modul pro práci s digitální osobností, který zahrnuje:
- Affective Profile - emoční a behaviorální parametry
- Reconstruction Engine - rekonstrukce odpovědí na základě imprintů
- Trace Replayer - analýza behaviorálních otisků
- Imprint Manager - správa LoRA imprintů

Tento modul rozšiřuje současnou architekturu NESTOR o biologicky inspirovaný
systém paměti a stylu, který umožňuje vytváření autentických digitálních osobností.
"""

from .affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait
from .reconstruction_engine import ReconstructionEngine, ReconstructionContext
from .trace_replayer import TraceReplayer, BehavioralTrace
from .imprint_manager import ImprintManager, PersonaImprint

__version__ = "0.1.0"
__author__ = "NESTOR Development Team"

__all__ = [
    "AffectiveProfile",
    "EmotionalState", 
    "BehavioralTrait",
    "ReconstructionEngine",
    "ReconstructionContext",
    "TraceReplayer",
    "BehavioralTrace",
    "ImprintManager",
    "PersonaImprint"
]
