"""
Trace Replayer - umožňuje opakovat nebo analyzovat behaviorální otisky

Tento modul implementuje systém pro záznam, analýzu a přehrávání behaviorálních
vzorců digitální osobnosti pro účely učení a ladění.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from enum import Enum
import structlog
import json

from .affective_profile import AffectiveProfile, EmotionalState, BehavioralTrait

logger = structlog.get_logger()


class TraceType(str, Enum):
    """Typy behaviorálních stop"""
    CONVERSATION = "conversation"      # Konverzační stopa
    EMOTIONAL = "emotional"           # Emoční reakce
    DECISION = "decision"             # Rozhodovací proces
    LEARNING = "learning"             # Učební proces
    ADAPTATION = "adaptation"         # Adaptační změny
    STYLE_SHIFT = "style_shift"       # Změny stylu


class BehavioralTrace(BaseModel):
    """Záznam behaviorální stopy"""
    
    # Identifikace
    id: str = Field(..., description="Unikátní ID stopy")
    persona_id: str = Field(..., description="ID osobnosti")
    trace_type: TraceType = Field(..., description="Typ stopy")
    
    # Kontext
    input_stimulus: str = Field(..., description="Vstupní podnět")
    output_response: str = Field(..., description="Výstupní odpověď")
    context_data: Dict[str, Any] = Field(default_factory=dict)
    
    # Stav v době záznamu
    emotional_state_before: EmotionalState
    emotional_state_after: EmotionalState
    active_traits: List[BehavioralTrait] = Field(default_factory=list)
    affective_vector: Dict[str, float] = Field(default_factory=dict)
    
    # Metadata procesu
    processing_time_ms: float = Field(default=0.0)
    confidence_score: float = Field(default=0.5, ge=0.0, le=1.0)
    used_memories: List[str] = Field(default_factory=list)
    style_signature: str = Field(default="")
    
    # Časové značky
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    session_id: Optional[str] = None
    conversation_turn: int = Field(default=0)
    
    # Výsledky a metriky
    user_feedback: Optional[str] = None
    effectiveness_score: Optional[float] = None
    learning_value: float = Field(default=0.5, ge=0.0, le=1.0)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TracePattern(BaseModel):
    """Vzorec chování identifikovaný v stopách"""
    
    pattern_id: str
    pattern_type: str
    frequency: int
    confidence: float
    triggers: List[str] = Field(default_factory=list)
    outcomes: List[str] = Field(default_factory=list)
    emotional_context: List[EmotionalState] = Field(default_factory=list)
    effectiveness: float = Field(default=0.5)
    
    first_occurrence: datetime
    last_occurrence: datetime
    evolution_trend: str = Field(default="stable")  # stable, improving, declining


class TraceAnalysis(BaseModel):
    """Výsledek analýzy behaviorálních stop"""
    
    total_traces: int
    time_period: Tuple[datetime, datetime]
    dominant_patterns: List[TracePattern]
    emotional_trends: Dict[EmotionalState, float]
    style_evolution: List[Dict[str, Any]]
    learning_insights: List[str]
    recommendations: List[str]
    
    # Metriky výkonnosti
    average_confidence: float
    response_consistency: float
    emotional_stability: float
    adaptation_rate: float


class TraceReplayer:
    """Systém pro záznam a analýzu behaviorálních stop"""
    
    def __init__(self, storage_backend=None):
        self.storage_backend = storage_backend
        self.trace_buffer: List[BehavioralTrace] = []
        self.analysis_cache: Dict[str, TraceAnalysis] = {}
        
    async def record_trace(
        self,
        persona_id: str,
        trace_type: TraceType,
        input_stimulus: str,
        output_response: str,
        affective_profile: AffectiveProfile,
        context: Optional[Dict[str, Any]] = None
    ) -> BehavioralTrace:
        """Zaznamenává novou behaviorální stopu"""
        
        trace = BehavioralTrace(
            id=f"trace_{datetime.utcnow().timestamp()}_{persona_id}",
            persona_id=persona_id,
            trace_type=trace_type,
            input_stimulus=input_stimulus,
            output_response=output_response,
            emotional_state_before=affective_profile.primary_emotion,
            emotional_state_after=affective_profile.primary_emotion,  # Bude aktualizováno
            active_traits=affective_profile.primary_traits,
            affective_vector={
                "valence": affective_profile.current_affective_state.valence,
                "arousal": affective_profile.current_affective_state.arousal,
                "dominance": affective_profile.current_affective_state.dominance,
                "intensity": affective_profile.current_affective_state.intensity
            },
            style_signature=affective_profile.get_style_signature(),
            context_data=context or {}
        )
        
        # Uložení do bufferu
        self.trace_buffer.append(trace)
        
        # Periodické ukládání
        if len(self.trace_buffer) >= 10:
            await self._flush_traces()
        
        logger.debug("Behaviorální stopa zaznamenána", 
                    trace_id=trace.id, 
                    trace_type=trace_type.value)
        
        return trace
    
    async def replay_sequence(
        self,
        persona_id: str,
        start_time: datetime,
        end_time: datetime,
        trace_types: Optional[List[TraceType]] = None
    ) -> List[BehavioralTrace]:
        """Přehraje sekvenci behaviorálních stop"""
        
        # Načtení stop z úložiště
        traces = await self._load_traces(persona_id, start_time, end_time, trace_types)
        
        logger.info("Přehrávání behaviorální sekvence",
                   persona_id=persona_id,
                   trace_count=len(traces),
                   time_span=(end_time - start_time).total_seconds())
        
        return sorted(traces, key=lambda t: t.timestamp)
    
    async def analyze_behavioral_patterns(
        self,
        persona_id: str,
        analysis_period: timedelta = timedelta(days=7),
        min_pattern_frequency: int = 3
    ) -> TraceAnalysis:
        """Analyzuje behaviorální vzorce v daném období"""
        
        end_time = datetime.utcnow()
        start_time = end_time - analysis_period
        
        # Načtení stop pro analýzu
        traces = await self._load_traces(persona_id, start_time, end_time)
        
        if not traces:
            return TraceAnalysis(
                total_traces=0,
                time_period=(start_time, end_time),
                dominant_patterns=[],
                emotional_trends={},
                style_evolution=[],
                learning_insights=[],
                recommendations=[],
                average_confidence=0.0,
                response_consistency=0.0,
                emotional_stability=0.0,
                adaptation_rate=0.0
            )
        
        # Analýza vzorců
        patterns = self._identify_patterns(traces, min_pattern_frequency)
        
        # Emoční trendy
        emotional_trends = self._analyze_emotional_trends(traces)
        
        # Evoluce stylu
        style_evolution = self._analyze_style_evolution(traces)
        
        # Výpočet metrik
        metrics = self._calculate_performance_metrics(traces)
        
        # Generování doporučení
        recommendations = self._generate_recommendations(patterns, emotional_trends, metrics)
        
        analysis = TraceAnalysis(
            total_traces=len(traces),
            time_period=(start_time, end_time),
            dominant_patterns=patterns,
            emotional_trends=emotional_trends,
            style_evolution=style_evolution,
            learning_insights=self._extract_learning_insights(traces),
            recommendations=recommendations,
            **metrics
        )
        
        # Cache výsledku
        cache_key = f"{persona_id}_{start_time.isoformat()}_{end_time.isoformat()}"
        self.analysis_cache[cache_key] = analysis
        
        logger.info("Analýza behaviorálních vzorců dokončena",
                   persona_id=persona_id,
                   patterns_found=len(patterns),
                   avg_confidence=metrics["average_confidence"])
        
        return analysis
    
    def _identify_patterns(
        self, 
        traces: List[BehavioralTrace], 
        min_frequency: int
    ) -> List[TracePattern]:
        """Identifikuje opakující se vzorce v behaviorálních stopách"""
        
        patterns = []
        
        # Skupinování podle typu a kontextu
        pattern_groups = {}
        
        for trace in traces:
            # Vytvoření klíče vzorce
            pattern_key = f"{trace.trace_type.value}_{trace.style_signature}_{trace.emotional_state_before.value}"
            
            if pattern_key not in pattern_groups:
                pattern_groups[pattern_key] = []
            
            pattern_groups[pattern_key].append(trace)
        
        # Analýza skupin
        for pattern_key, group_traces in pattern_groups.items():
            if len(group_traces) >= min_frequency:
                # Výpočet statistik vzorce
                avg_confidence = sum(t.confidence_score for t in group_traces) / len(group_traces)
                avg_effectiveness = sum(
                    t.effectiveness_score for t in group_traces 
                    if t.effectiveness_score is not None
                ) / max(1, sum(1 for t in group_traces if t.effectiveness_score is not None))
                
                pattern = TracePattern(
                    pattern_id=pattern_key,
                    pattern_type=group_traces[0].trace_type.value,
                    frequency=len(group_traces),
                    confidence=avg_confidence,
                    triggers=list(set(t.input_stimulus[:50] for t in group_traces)),
                    outcomes=list(set(t.output_response[:50] for t in group_traces)),
                    emotional_context=list(set(t.emotional_state_before for t in group_traces)),
                    effectiveness=avg_effectiveness,
                    first_occurrence=min(t.timestamp for t in group_traces),
                    last_occurrence=max(t.timestamp for t in group_traces)
                )
                
                patterns.append(pattern)
        
        return sorted(patterns, key=lambda p: p.frequency, reverse=True)
    
    def _analyze_emotional_trends(self, traces: List[BehavioralTrace]) -> Dict[EmotionalState, float]:
        """Analyzuje emoční trendy v stopách"""
        
        emotion_counts = {}
        total_traces = len(traces)
        
        for trace in traces:
            emotion = trace.emotional_state_before
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # Převod na procenta
        return {
            emotion: count / total_traces 
            for emotion, count in emotion_counts.items()
        }
    
    def _analyze_style_evolution(self, traces: List[BehavioralTrace]) -> List[Dict[str, Any]]:
        """Analyzuje evoluci stylu v čase"""
        
        # Rozdělení stop do časových oken
        time_windows = self._create_time_windows(traces, window_size=timedelta(hours=6))
        
        evolution = []
        for window_start, window_traces in time_windows.items():
            if window_traces:
                style_signatures = [t.style_signature for t in window_traces]
                dominant_style = max(set(style_signatures), key=style_signatures.count)
                
                evolution.append({
                    "timestamp": window_start.isoformat(),
                    "dominant_style": dominant_style,
                    "style_diversity": len(set(style_signatures)),
                    "trace_count": len(window_traces)
                })
        
        return evolution
    
    def _calculate_performance_metrics(self, traces: List[BehavioralTrace]) -> Dict[str, float]:
        """Vypočítá výkonnostní metriky"""
        
        if not traces:
            return {
                "average_confidence": 0.0,
                "response_consistency": 0.0,
                "emotional_stability": 0.0,
                "adaptation_rate": 0.0
            }
        
        # Průměrná confidence
        avg_confidence = sum(t.confidence_score for t in traces) / len(traces)
        
        # Konzistence odpovědí (podobnost stylových podpisů)
        style_signatures = [t.style_signature for t in traces]
        unique_styles = len(set(style_signatures))
        response_consistency = 1.0 - (unique_styles / len(traces))
        
        # Emoční stabilita
        emotion_changes = sum(
            1 for i in range(1, len(traces))
            if traces[i].emotional_state_before != traces[i-1].emotional_state_after
        )
        emotional_stability = 1.0 - (emotion_changes / max(1, len(traces) - 1))
        
        # Rychlost adaptace (změny v learning_value)
        learning_values = [t.learning_value for t in traces]
        adaptation_rate = sum(learning_values) / len(learning_values)
        
        return {
            "average_confidence": avg_confidence,
            "response_consistency": response_consistency,
            "emotional_stability": emotional_stability,
            "adaptation_rate": adaptation_rate
        }
    
    def _generate_recommendations(
        self,
        patterns: List[TracePattern],
        emotional_trends: Dict[EmotionalState, float],
        metrics: Dict[str, float]
    ) -> List[str]:
        """Generuje doporučení na základě analýzy"""
        
        recommendations = []
        
        # Doporučení na základě vzorců
        if patterns:
            most_frequent = patterns[0]
            if most_frequent.effectiveness < 0.6:
                recommendations.append(
                    f"Vzorec '{most_frequent.pattern_type}' má nízkou efektivnost. "
                    "Zvažte úpravu stylových parametrů."
                )
        
        # Doporučení na základě emočních trendů
        dominant_emotion = max(emotional_trends.items(), key=lambda x: x[1])
        if dominant_emotion[1] > 0.7:
            recommendations.append(
                f"Dominantní emoce '{dominant_emotion[0].value}' může omezovat "
                "rozmanitost odpovědí. Zvažte rozšíření emočního spektra."
            )
        
        # Doporučení na základě metrik
        if metrics["response_consistency"] < 0.5:
            recommendations.append(
                "Nízká konzistence odpovědí. Zvažte stabilizaci stylových parametrů."
            )
        
        if metrics["adaptation_rate"] < 0.3:
            recommendations.append(
                "Pomalá adaptace. Zvažte zvýšení learning_rate parametru."
            )
        
        return recommendations
    
    def _extract_learning_insights(self, traces: List[BehavioralTrace]) -> List[str]:
        """Extrahuje poznatky pro učení"""
        
        insights = []
        
        # Analýza úspěšných interakcí
        successful_traces = [t for t in traces if t.effectiveness_score and t.effectiveness_score > 0.8]
        if successful_traces:
            common_traits = self._find_common_traits(successful_traces)
            if common_traits:
                insights.append(f"Úspěšné interakce často obsahují rysy: {', '.join(common_traits)}")
        
        # Analýza problematických oblastí
        low_confidence_traces = [t for t in traces if t.confidence_score < 0.4]
        if len(low_confidence_traces) > len(traces) * 0.2:
            insights.append("Vysoký podíl odpovědí s nízkou confidence - potřeba více tréninku")
        
        return insights
    
    def _find_common_traits(self, traces: List[BehavioralTrace]) -> List[str]:
        """Najde společné rysy v sadě stop"""
        
        trait_counts = {}
        for trace in traces:
            for trait in trace.active_traits:
                trait_counts[trait.value] = trait_counts.get(trait.value, 0) + 1
        
        # Vrátí rysy, které se objevují ve více než 50% stop
        threshold = len(traces) * 0.5
        return [trait for trait, count in trait_counts.items() if count >= threshold]
    
    def _create_time_windows(
        self, 
        traces: List[BehavioralTrace], 
        window_size: timedelta
    ) -> Dict[datetime, List[BehavioralTrace]]:
        """Vytvoří časová okna pro analýzu"""
        
        if not traces:
            return {}
        
        # Seřazení podle času
        sorted_traces = sorted(traces, key=lambda t: t.timestamp)
        
        windows = {}
        current_window_start = sorted_traces[0].timestamp.replace(minute=0, second=0, microsecond=0)
        
        for trace in sorted_traces:
            # Najdi správné okno
            while trace.timestamp >= current_window_start + window_size:
                current_window_start += window_size
            
            if current_window_start not in windows:
                windows[current_window_start] = []
            
            windows[current_window_start].append(trace)
        
        return windows
    
    async def _flush_traces(self):
        """Uloží buffered stopy do úložiště"""
        
        if self.storage_backend and self.trace_buffer:
            try:
                await self.storage_backend.store_traces(self.trace_buffer)
                logger.debug("Behaviorální stopy uloženy", count=len(self.trace_buffer))
                self.trace_buffer.clear()
            except Exception as e:
                logger.error("Chyba při ukládání stop", error=str(e))
    
    async def _load_traces(
        self,
        persona_id: str,
        start_time: datetime,
        end_time: datetime,
        trace_types: Optional[List[TraceType]] = None
    ) -> List[BehavioralTrace]:
        """Načte stopy z úložiště"""
        
        if not self.storage_backend:
            return self.trace_buffer  # Fallback na buffer
        
        try:
            return await self.storage_backend.load_traces(
                persona_id, start_time, end_time, trace_types
            )
        except Exception as e:
            logger.error("Chyba při načítání stop", error=str(e))
            return []
