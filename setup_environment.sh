#!/bin/bash

# NESTOR Development Environment Setup Script
# Automaticky připraví lokální vývojové prostředí po klonování z GitHubu
# Podporuje Linux distribuce s Podman

set -e  # Ukončit při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkce pro barevný výstup
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Detekce distribuce
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        print_error "Nelze detekovat Linux distribuci"
        exit 1
    fi
    print_info "Detekována distribuce: $DISTRO $VERSION"
}

# Kontrola a instalace Podmanu
check_install_podman() {
    print_header "KONTROLA A INSTALACE PODMANU"

    if command -v podman &> /dev/null; then
        PODMAN_VERSION=$(podman --version | cut -d' ' -f3)
        print_success "Podman je již nainstalován (verze: $PODMAN_VERSION)"
        return 0
    fi

    print_warning "Podman není nainstalován. Pokusím se o instalaci..."

    case $DISTRO in
        "fedora")
            print_info "Instaluji Podman na Fedora..."
            sudo dnf install -y podman podman-compose
            ;;
        "ubuntu"|"debian")
            print_info "Instaluji Podman na Ubuntu/Debian..."
            sudo apt-get update
            sudo apt-get install -y podman
            # Podman Compose může vyžadovat pip instalaci
            if ! command -v podman-compose &> /dev/null; then
                print_info "Instaluji podman-compose přes pip..."
                sudo apt-get install -y python3-pip
                pip3 install podman-compose
            fi
            ;;
        "arch"|"manjaro")
            print_info "Instaluji Podman na Arch Linux..."
            sudo pacman -S --noconfirm podman podman-compose
            ;;
        "opensuse"|"opensuse-leap"|"opensuse-tumbleweed")
            print_info "Instaluji Podman na openSUSE..."
            sudo zypper install -y podman podman-compose
            ;;
        *)
            print_error "Nepodporovaná distribuce: $DISTRO"
            print_info "Prosím nainstalujte Podman manuálně podle dokumentace:"
            print_info "https://podman.io/getting-started/installation"
            exit 1
            ;;
    esac

    # Ověření instalace
    if command -v podman &> /dev/null; then
        PODMAN_VERSION=$(podman --version | cut -d' ' -f3)
        print_success "Podman úspěšně nainstalován (verze: $PODMAN_VERSION)"
    else
        print_error "Instalace Podmanu selhala"
        exit 1
    fi
}

# Kontrola podman-compose
check_podman_compose() {
    print_header "KONTROLA PODMAN-COMPOSE"

    if command -v podman-compose &> /dev/null; then
        print_success "podman-compose je dostupný"
        return 0
    fi

    print_warning "podman-compose není dostupný. Pokusím se o instalaci..."

    # Pokus o instalaci přes pip
    if command -v pip3 &> /dev/null; then
        print_info "Instaluji podman-compose přes pip3..."
        pip3 install --user podman-compose

        # Přidání do PATH pokud není
        if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
            export PATH="$HOME/.local/bin:$PATH"
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
            print_info "Přidán ~/.local/bin do PATH"
        fi
    else
        print_error "pip3 není dostupný. Nainstalujte python3-pip a poté spusťte:"
        print_info "pip3 install --user podman-compose"
        exit 1
    fi

    # Ověření instalace
    if command -v podman-compose &> /dev/null; then
        print_success "podman-compose úspěšně nainstalován"
    else
        print_error "Instalace podman-compose selhala"
        exit 1
    fi
}

# Kontrola závislostí
check_dependencies() {
    print_header "KONTROLA ZÁVISLOSTÍ"

    local missing_deps=()

    # Kontrola curl (pro health checks)
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi

    # Kontrola git
    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi

    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_warning "Chybějící závislosti: ${missing_deps[*]}"
        print_info "Pokusím se o instalaci..."

        case $DISTRO in
            "fedora")
                sudo dnf install -y "${missing_deps[@]}"
                ;;
            "ubuntu"|"debian")
                sudo apt-get update
                sudo apt-get install -y "${missing_deps[@]}"
                ;;
            "arch"|"manjaro")
                sudo pacman -S --noconfirm "${missing_deps[@]}"
                ;;
            "opensuse"|"opensuse-leap"|"opensuse-tumbleweed")
                sudo zypper install -y "${missing_deps[@]}"
                ;;
        esac
    else
        print_success "Všechny závislosti jsou dostupné"
    fi
}

# Kontrola .env souboru
check_env_file() {
    print_header "KONTROLA ENVIRONMENT VARIABLES"

    if [ ! -f .env ]; then
        print_warning ".env soubor neexistuje. Vytvářím z template..."
        cat > .env << 'EOF'
# NESTOR Environment Variables
# Zkopírujte tento soubor jako .env a upravte hodnoty podle potřeby

# API klíče pro LLM služby (volitelné pro lokální vývoj)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=

# Blockchain konfigurace (volitelné)
BLOCKCHAIN_NETWORK=ethereum
BLOCKCHAIN_RPC_URL=

# Databáze (používá se z compose.yml)
POSTGRES_DB=nestor
POSTGRES_USER=nestor
POSTGRES_PASSWORD=nestor_password

# Logování
LOG_LEVEL=INFO

# Produkční nastavení (pro deployment)
ENVIRONMENT=development
SECRET_KEY=your-secret-key-change-in-production
EOF
        print_success ".env soubor vytvořen. Upravte API klíče podle potřeby."
    else
        print_success ".env soubor již existuje"
    fi
}

# Vytvoření adresářů
create_directories() {
    print_header "VYTVÁŘENÍ ADRESÁŘŮ"

    local dirs=(
        "logs"
        "data/backups"
        "data/seeds"
        "scripts"
    )

    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "Vytvořen adresář: $dir"
        fi
    done

    print_success "Všechny potřebné adresáře jsou připraveny"
}

# Spuštění služeb
start_services() {
    print_header "SPOUŠTĚNÍ SLUŽEB"

    print_info "Spouštím Podman Compose..."

    # Zastavení případně běžících kontejnerů
    if podman-compose -f podman-compose.yml ps | grep -q "Up"; then
        print_info "Zastavuji běžící kontejnery..."
        podman-compose -f podman-compose.yml down
    fi

    # Spuštění služeb
    print_info "Spouštím všechny služby..."
    podman-compose -f podman-compose.yml up -d

    print_info "Čekám na spuštění služeb..."
    sleep 10

    # Kontrola stavu služeb
    print_info "Kontroluji stav služeb..."
    podman-compose -f podman-compose.yml ps
}

# Inicializace databáze
init_database() {
    print_header "INICIALIZACE DATABÁZE"

    print_info "Čekám na dostupnost PostgreSQL..."

    # Čekání na PostgreSQL
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if podman exec nestor-postgres pg_isready -U nestor -d nestor &> /dev/null; then
            print_success "PostgreSQL je dostupný"
            break
        fi

        print_info "Pokus $attempt/$max_attempts - čekám na PostgreSQL..."
        sleep 2
        ((attempt++))
    done

    if [ $attempt -gt $max_attempts ]; then
        print_error "PostgreSQL není dostupný po $max_attempts pokusech"
        exit 1
    fi

    # Spuštění inicializačního SQL
    print_info "Spouštím inicializační SQL skript..."
    podman exec -i nestor-postgres psql -U nestor -d nestor < data/migrations/01_init.sql

    # Spuštění seed dat pokud existují
    if [ -f "data/seeds/sample_data.sql" ]; then
        print_info "Načítám ukázková data..."
        podman exec -i nestor-postgres psql -U nestor -d nestor < data/seeds/sample_data.sql
    fi

    print_success "Databáze byla úspěšně inicializována"
}

# Kontrola zdraví služeb
check_services_health() {
    print_header "KONTROLA ZDRAVÍ SLUŽEB"

    local services=(
        "http://localhost:5432|PostgreSQL|pg_isready"
        "http://localhost:6379|Redis|redis-cli ping"
        "http://localhost:8000|Main API|curl -f"
        "http://localhost:8001|Memory Service|curl -f"
        "http://localhost:8002|RAG Service|curl -f"
        "http://localhost:8003|LLM Interface|curl -f"
        "http://localhost:8004|Tokenization|curl -f"
    )

    for service in "${services[@]}"; do
        IFS='|' read -r url name check <<< "$service"

        if [[ $name == "PostgreSQL" ]]; then
            if podman exec nestor-postgres pg_isready -U nestor -d nestor &> /dev/null; then
                print_success "$name je zdravý"
            else
                print_warning "$name není dostupný"
            fi
        elif [[ $name == "Redis" ]]; then
            if podman exec nestor-redis redis-cli ping &> /dev/null; then
                print_success "$name je zdravý"
            else
                print_warning "$name není dostupný"
            fi
        else
            if curl -f "${url}/health" &> /dev/null; then
                print_success "$name je zdravý"
            else
                print_warning "$name není dostupný na ${url}/health"
            fi
        fi
    done
}

# Zobrazení informací o spuštěných službách
show_service_info() {
    print_header "INFORMACE O SLUŽBÁCH"

    echo -e "${GREEN}NESTOR Development Environment je připraven!${NC}\n"

    echo "Dostupné služby:"
    echo "• Main API:          http://localhost:8000"
    echo "• Memory Service:    http://localhost:8001"
    echo "• RAG Service:       http://localhost:8002"
    echo "• LLM Interface:     http://localhost:8003"
    echo "• Tokenization:      http://localhost:8004"
    echo "• PostgreSQL:        localhost:5432"
    echo "• Redis:             localhost:6379"
    echo ""
    echo "Užitečné příkazy:"
    echo "• Zobrazit logy:     podman-compose -f podman-compose.yml logs -f"
    echo "• Zastavit služby:   podman-compose -f podman-compose.yml down"
    echo "• Restartovat:       podman-compose -f podman-compose.yml restart"
    echo "• Stav služeb:       podman-compose -f podman-compose.yml ps"
    echo ""
    echo "Pro zálohu dat použijte: ./scripts/backup_volumes.sh"
    echo "Pro obnovení dat použijte: ./scripts/restore_volumes.sh"
}

# Hlavní funkce
main() {
    print_header "NESTOR DEVELOPMENT ENVIRONMENT SETUP"

    # Kontrola, že jsme v root adresáři projektu
    if [ ! -f "podman-compose.yml" ]; then
        print_error "Tento skript musí být spuštěn z root adresáře NESTOR projektu"
        print_info "Ujistěte se, že jste v adresáři obsahujícím podman-compose.yml"
        exit 1
    fi

    detect_distro
    check_install_podman
    check_podman_compose
    check_dependencies
    check_env_file
    create_directories
    start_services
    init_database
    check_services_health
    show_service_info

    print_success "Setup dokončen úspěšně!"
}

# Spuštění hlavní funkce
main "$@"
