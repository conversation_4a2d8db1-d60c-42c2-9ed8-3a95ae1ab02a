# NESTOR Environment Variables Template
# Zkopírujte tento soubor jako .env a upravte hodnoty podle potřeby

# =============================================================================
# API KLÍČE PRO LLM SLUŽBY
# =============================================================================
# Volitelné pro lokální vývoj, povinné pro produkci

# OpenAI API klíč pro GPT modely
OPENAI_API_KEY=

# Anthropic API klíč pro Claude modely  
ANTHROPIC_API_KEY=

# =============================================================================
# BLOCKCHAIN KONFIGURACE
# =============================================================================
# Konfigurace pro tokenizační službu

# Blockchain síť (ethereum, polygon, binance, etc.)
BLOCKCHAIN_NETWORK=ethereum

# RPC URL pro připojení k blockchain síti
BLOCKCHAIN_RPC_URL=

# Privátní klíč pro blockchain transakce (pouze pro testování!)
# NIKDY NEPOUŽÍVEJTE PRODUKČNÍ KLÍČE V .env SOUBORU!
BLOCKCHAIN_PRIVATE_KEY=

# =============================================================================
# DATABÁZE
# =============================================================================
# Tyto hodnoty se používají z podman-compose.yml

POSTGRES_DB=nestor
POSTGRES_USER=nestor
POSTGRES_PASSWORD=nestor_password

# Kompletní databázové URL (generuje se automaticky)
DATABASE_URL=*************************************************/nestor

# =============================================================================
# REDIS
# =============================================================================
# Redis konfigurace pro cache a message broker

REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# =============================================================================
# LOGOVÁNÍ
# =============================================================================
# Úroveň logování pro všechny služby

LOG_LEVEL=INFO

# Možné hodnoty: DEBUG, INFO, WARNING, ERROR, CRITICAL

# =============================================================================
# APLIKAČNÍ NASTAVENÍ
# =============================================================================

# Prostředí aplikace
ENVIRONMENT=development

# Tajný klíč pro JWT tokeny a šifrování
# V produkci použijte silný, náhodně generovaný klíč!
SECRET_KEY=your-secret-key-change-in-production

# CORS nastavení (pro frontend)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# SLUŽBY URL
# =============================================================================
# URL pro komunikaci mezi mikroslužbami (používá se z compose.yml)

MEMORY_SERVICE_URL=http://memory:8000
RAG_SERVICE_URL=http://vectorstore:8000
LLM_SERVICE_URL=http://llm_interface:8000
TOKENIZATION_SERVICE_URL=http://tokenization:8000

# =============================================================================
# EMBEDDING MODELY
# =============================================================================
# Konfigurace pro vektorové embeddings

# Model pro generování embeddings
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Dimenze vektorů (musí odpovídat modelu)
EMBEDDING_DIMENSION=384

# Batch size pro zpracování embeddings
EMBEDDING_BATCH_SIZE=32

# =============================================================================
# MONITORING A METRIKY
# =============================================================================

# Povolení Prometheus metrik
ENABLE_METRICS=true

# Port pro metriky
METRICS_PORT=9090

# =============================================================================
# BEZPEČNOST
# =============================================================================

# JWT token expiration (v sekundách)
JWT_EXPIRATION=3600

# Maximální velikost uploadovaných souborů (v MB)
MAX_FILE_SIZE=100

# Rate limiting (požadavky za minutu)
RATE_LIMIT=100

# =============================================================================
# VÝVOJÁŘSKÉ NASTAVENÍ
# =============================================================================

# Debug režim (pouze pro vývoj!)
DEBUG=false

# Automatické reload při změnách kódu
AUTO_RELOAD=true

# Povolení CORS pro všechny origins (pouze pro vývoj!)
CORS_ALLOW_ALL=false

# =============================================================================
# PRODUKČNÍ NASTAVENÍ
# =============================================================================
# Tyto hodnoty použijte pouze v produkčním prostředí

# Produkční databázové URL
# PRODUCTION_DATABASE_URL=postgresql://user:password@host:port/database

# Produkční Redis URL
# PRODUCTION_REDIS_URL=redis://user:password@host:port

# SSL certifikáty
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# =============================================================================
# POZNÁMKY
# =============================================================================

# 1. Tento soubor obsahuje citlivé informace - NIKDY ho necommitujte do Gitu!
# 2. V produkci použijte silné hesla a náhodně generované klíče
# 3. API klíče získáte na:
#    - OpenAI: https://platform.openai.com/api-keys
#    - Anthropic: https://console.anthropic.com/
# 4. Pro blockchain RPC můžete použít:
#    - Infura: https://infura.io/
#    - Alchemy: https://www.alchemy.com/
#    - QuickNode: https://www.quicknode.com/
# 5. Všechny hodnoty s prázdnou hodnotou jsou volitelné pro lokální vývoj
