"""
Generation Service - generování textu pomocí LLM s podporou persona_core
"""

from typing import List, Optional, Dict, Any, Union
import structlog
import httpx
import json

# Import persona_core modulů
from persona_core.affective_profile import AffectiveProfile
from persona_core.reconstruction_engine import ReconstructionEngine, ReconstructionContext
from persona_core.imprint_manager import ImprintManager

logger = structlog.get_logger()

class GenerationService:
    """Služba pro generování textu pomocí LLM s podporou persona_core"""

    def __init__(self, model_service, redis_url: str):
        self.model_service = model_service
        self.redis_url = redis_url
        self.http_client = None

        # Persona core komponenty
        self.reconstruction_engine = None
        self.imprint_manager = None

    async def initialize(self):
        """Inicializace generation služby"""
        self.http_client = httpx.AsyncClient(timeout=60.0)

        # Inicializace persona_core komponent
        self.reconstruction_engine = ReconstructionEngine(
            memory_manager=None,  # Bude nastaveno později
            llm_service=self
        )
        self.imprint_manager = ImprintManager(
            storage_backend=None,  # Bude nastaveno později
            training_backend=None  # Bude nastaveno později
        )

        logger.info("Generation Service inicializován s persona_core podporou")

    async def cleanup(self):
        """Úklid při ukončování"""
        if self.http_client:
            await self.http_client.aclose()

    async def generate(
        self,
        prompt: str,
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 150,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Generování textu pomocí LLM"""

        try:
            # Kontrola dostupnosti modelu
            available_models = await self.model_service.get_available_models()
            model_names = [m.name for m in available_models]

            if model not in model_names:
                # Fallback na dostupný model
                model = model_names[0] if model_names else "mock"
                logger.warning("Model není dostupný, používám fallback", requested=model, fallback=model)

            # Pro demo účely - mock implementace
            if model == "mock" or not self.model_service.openai_api_key:
                return await self._mock_generate(prompt, model, max_tokens)

            # OpenAI API volání
            if model.startswith("gpt"):
                return await self._openai_generate(
                    prompt, model, max_tokens, temperature, top_p,
                    frequency_penalty, presence_penalty, stop, stream
                )

            # Anthropic API volání
            elif model.startswith("claude"):
                return await self._anthropic_generate(
                    prompt, model, max_tokens, temperature, top_p, stop
                )

            # Local model
            else:
                return await self._local_generate(
                    prompt, model, max_tokens, temperature, stop
                )

        except Exception as e:
            logger.error("Chyba při generování textu", error=str(e), model=model)
            # Fallback na mock
            return await self._mock_generate(prompt, model, max_tokens)

    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 150,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Chat completion pomocí LLM"""

        try:
            # Pro demo účely - mock implementace
            if model == "mock" or not self.model_service.openai_api_key:
                return await self._mock_chat(messages, model, max_tokens)

            # OpenAI API volání
            if model.startswith("gpt"):
                return await self._openai_chat(
                    messages, model, max_tokens, temperature, top_p,
                    frequency_penalty, presence_penalty, stop, stream
                )

            # Anthropic API volání
            elif model.startswith("claude"):
                return await self._anthropic_chat(
                    messages, model, max_tokens, temperature, stop
                )

            # Local model
            else:
                return await self._local_chat(
                    messages, model, max_tokens, temperature, stop
                )

        except Exception as e:
            logger.error("Chyba při chat completion", error=str(e), model=model)
            # Fallback na mock
            return await self._mock_chat(messages, model, max_tokens)

    async def generate_with_persona(
        self,
        prompt: str,
        persona_id: str,
        affective_profile: Optional[AffectiveProfile] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 150,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """Generování textu s podporou digitální osobnosti"""

        try:
            # Načtení aktivního imprintu pro osobnost
            active_imprint = None
            if self.imprint_manager:
                active_imprint = self.imprint_manager.get_active_imprint(persona_id)

            # Pokud není affective_profile poskytnut, vytvoříme základní
            if not affective_profile:
                affective_profile = AffectiveProfile(
                    persona_id=persona_id,
                    name=f"persona_{persona_id}"
                )

            # Použití reconstruction engine pokud je dostupný
            if self.reconstruction_engine:
                context = ReconstructionContext(
                    persona_id=persona_id,
                    input_text=prompt,
                    conversation_history=conversation_history or [],
                    current_emotion=affective_profile.primary_emotion
                )

                # Rekonstrukce odpovědi pomocí persona_core
                reconstruction_result = await self.reconstruction_engine.reconstruct_response(
                    context=context,
                    affective_profile=affective_profile,
                    imprint_data=active_imprint.dict() if active_imprint else None
                )

                # Aktualizace metrik imprintu
                if active_imprint and self.imprint_manager:
                    await self.imprint_manager.update_imprint_metrics(
                        active_imprint.id,
                        success=reconstruction_result.confidence_score > 0.7
                    )

                return {
                    "text": reconstruction_result.reconstructed_text,
                    "model": f"{model} (persona-enhanced)",
                    "persona_id": persona_id,
                    "style_signature": reconstruction_result.style_signature,
                    "emotional_coloring": reconstruction_result.emotional_coloring.value,
                    "confidence_score": reconstruction_result.confidence_score,
                    "used_memories": reconstruction_result.used_memories,
                    "metadata": {
                        "persona_enhanced": True,
                        "active_imprint": active_imprint.id if active_imprint else None,
                        "reconstruction_metadata": reconstruction_result.metadata
                    }
                }

            # Fallback na standardní generování s persona informacemi
            enhanced_prompt = self._enhance_prompt_with_persona(prompt, affective_profile)

            result = await self.generate(
                prompt=enhanced_prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature
            )

            # Přidání persona metadat
            result["persona_id"] = persona_id
            result["style_signature"] = affective_profile.get_style_signature()
            result["emotional_coloring"] = affective_profile.primary_emotion.value
            result["metadata"]["persona_enhanced"] = True

            return result

        except Exception as e:
            logger.error("Chyba při generování s persona",
                        error=str(e),
                        persona_id=persona_id)

            # Fallback na standardní generování
            return await self.generate(prompt, model, max_tokens, temperature)

    def _enhance_prompt_with_persona(self, prompt: str, affective_profile: AffectiveProfile) -> str:
        """Vylepší prompt o informace o osobnosti"""

        # Základní persona informace
        persona_info = f"Jsi {affective_profile.name}, digitální osobnost s následujícími charakteristikami:\n"

        # Emoční stav
        persona_info += f"- Aktuální emoční stav: {affective_profile.primary_emotion.value}\n"

        # Behaviorální rysy
        if affective_profile.primary_traits:
            traits_str = ", ".join([trait.value for trait in affective_profile.primary_traits])
            persona_info += f"- Hlavní charakterové rysy: {traits_str}\n"

        # Komunikační styl
        persona_info += f"- Komunikační styl: {affective_profile.communication_style}\n"

        # Jazykové markery
        if affective_profile.linguistic_markers:
            markers_str = ", ".join(affective_profile.linguistic_markers)
            persona_info += f"- Jazykové prvky: {markers_str}\n"

        # Kombinace s původním promptem
        enhanced_prompt = f"{persona_info}\nUživatel: {prompt}\nOdpověz jako {affective_profile.name}:"

        return enhanced_prompt

    async def _mock_generate(self, prompt: str, model: str, max_tokens: int) -> Dict[str, Any]:
        """Mock implementace pro generování"""

        # Jednoduchá mock odpověď
        if "hello" in prompt.lower():
            text = "Hello! How can I help you today?"
        elif "what" in prompt.lower():
            text = "That's an interesting question. Let me think about it..."
        elif "nestor" in prompt.lower():
            text = "NESTOR is an innovative platform for creating and tokenizing digital personalities using advanced AI technologies."
        else:
            text = f"This is a mock response to your prompt. You asked about: {prompt[:50]}..."

        return {
            "text": text,
            "model": f"{model} (mock)",
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(text.split()),
                "total_tokens": len(prompt.split()) + len(text.split())
            },
            "metadata": {
                "mock": True,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }

    async def _mock_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int) -> Dict[str, Any]:
        """Mock implementace pro chat"""

        last_message = messages[-1]["content"] if messages else ""

        # Jednoduchá mock odpověď
        if "hello" in last_message.lower():
            content = "Hello! I'm a digital personality. How can I assist you?"
        elif "how are you" in last_message.lower():
            content = "I'm doing well, thank you for asking! I'm here to help you with any questions."
        elif "nestor" in last_message.lower():
            content = "NESTOR is a fascinating project that combines AI, blockchain, and VR technologies to create authentic digital personalities."
        else:
            content = f"I understand you're asking about: {last_message[:50]}... Let me help you with that."

        return {
            "message": {
                "role": "assistant",
                "content": content
            },
            "model": f"{model} (mock)",
            "usage": {
                "prompt_tokens": sum(len(msg["content"].split()) for msg in messages),
                "completion_tokens": len(content.split()),
                "total_tokens": sum(len(msg["content"].split()) for msg in messages) + len(content.split())
            },
            "metadata": {
                "mock": True,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }

    async def _openai_generate(self, prompt: str, model: str, max_tokens: int,
                              temperature: float, top_p: float, frequency_penalty: float,
                              presence_penalty: float, stop: Optional[List[str]], stream: bool) -> Dict[str, Any]:
        """OpenAI API generování"""
        # TODO: Implementovat skutečné OpenAI API volání
        return await self._mock_generate(prompt, model, max_tokens)

    async def _openai_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                          temperature: float, top_p: float, frequency_penalty: float,
                          presence_penalty: float, stop: Optional[List[str]], stream: bool) -> Dict[str, Any]:
        """OpenAI API chat completion"""
        # TODO: Implementovat skutečné OpenAI API volání
        return await self._mock_chat(messages, model, max_tokens)

    async def _anthropic_generate(self, prompt: str, model: str, max_tokens: int,
                                 temperature: float, top_p: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Anthropic API generování"""
        # TODO: Implementovat skutečné Anthropic API volání
        return await self._mock_generate(prompt, model, max_tokens)

    async def _anthropic_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                             temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Anthropic API chat completion"""
        # TODO: Implementovat skutečné Anthropic API volání
        return await self._mock_chat(messages, model, max_tokens)

    async def _local_generate(self, prompt: str, model: str, max_tokens: int,
                             temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Local model generování"""
        # TODO: Implementovat lokální model
        return await self._mock_generate(prompt, model, max_tokens)

    async def _local_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                         temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Local model chat completion"""
        # TODO: Implementovat lokální model
        return await self._mock_chat(messages, model, max_tokens)
