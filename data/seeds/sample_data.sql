-- NESTOR Sample Data
-- <PERSON>k<PERSON>zkov<PERSON> data pro rychlé testování systému

-- <PERSON><PERSON><PERSON><PERSON><PERSON> dalš<PERSON>ch testovacích uživatelů
INSERT INTO users (username, email, password_hash, full_name, is_verified) VALUES 
('testuser', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Test User', true),
('demo', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Demo User', true)
ON CONFLICT (username) DO NOTHING;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> ID uživatelů pro další vložení
DO $$
DECLARE
    admin_user_id UUID;
    test_user_id UUID;
    demo_user_id UUID;
    nestor_personality_id UUID;
    einstein_personality_id UUID;
    shakespeare_personality_id UUID;
    conversation_id UUID;
BEGIN
    -- <PERSON><PERSON><PERSON><PERSON><PERSON> ID uživatelů
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    SELECT id INTO test_user_id FROM users WHERE username = 'testuser';
    SELECT id INTO demo_user_id FROM users WHERE username = 'demo';
    
    -- Vložení dalších testovacích osobností
    INSERT INTO personalities (user_id, name, description, metadata) VALUES 
    (test_user_id, 'Albert Einstein', 'Digitální reprezentace Alberta Einsteina s jeho vědeckými znalostmi a filozofickým přístupem', 
     '{"era": "1879-1955", "field": "physics", "specialization": ["relativity", "quantum_mechanics"], "personality_traits": ["curious", "thoughtful", "humorous"]}'),
    (demo_user_id, 'William Shakespeare', 'Digitální osobnost největšího dramatika a básníka anglické literatury', 
     '{"era": "1564-1616", "field": "literature", "specialization": ["drama", "poetry", "sonnets"], "personality_traits": ["creative", "eloquent", "passionate"]}')
    ON CONFLICT DO NOTHING;
    
    -- Získání ID osobností
    SELECT id INTO nestor_personality_id FROM personalities WHERE name = 'NESTOR Demo';
    SELECT id INTO einstein_personality_id FROM personalities WHERE name = 'Albert Einstein';
    SELECT id INTO shakespeare_personality_id FROM personalities WHERE name = 'William Shakespeare';
    
    -- Vložení ukázkových pamětí pro Einstein
    INSERT INTO memories (content, memory_type, personality_id, importance, metadata) VALUES 
    ('Teorie relativity změnila naše chápání prostoru a času. E=mc² ukazuje ekvivalenci hmoty a energie.', 'semantic', einstein_personality_id, 0.9, 
     '{"topic": "relativity", "year": "1905", "type": "scientific_discovery"}'),
    ('Pamatuji si svou první přednášku na Princetonské univerzitě. Studenti byli velmi zvědaví na kvantovou mechaniku.', 'episodic', einstein_personality_id, 0.7,
     '{"location": "Princeton", "event": "lecture", "audience": "students"}'),
    ('Fantazie je důležitější než znalosti. Znalosti jsou omezené, fantazie objímá celý svět.', 'semantic', einstein_personality_id, 0.8,
     '{"type": "philosophy", "theme": "creativity_vs_knowledge"}'),
    ('Když pracuji na složitých problémech, často si hraju na housle. Hudba mi pomáhá myslet.', 'procedural', einstein_personality_id, 0.6,
     '{"activity": "violin_playing", "purpose": "problem_solving", "hobby": true}');
    
    -- Vložení ukázkových pamětí pro Shakespeare
    INSERT INTO memories (content, memory_type, personality_id, importance, metadata) VALUES 
    ('Být či nebýt, to je otázka. Tato věta z Hamleta vyjadřuje základní existenciální dilema.', 'semantic', shakespeare_personality_id, 0.9,
     '{"work": "Hamlet", "theme": "existentialism", "type": "famous_quote"}'),
    ('Vzpomínám si na premiéru Romea a Julie v Globe Theatre. Publikum bylo zcela pohrouženo do příběhu.', 'episodic', shakespeare_personality_id, 0.8,
     '{"location": "Globe Theatre", "work": "Romeo and Juliet", "event": "premiere"}'),
    ('Při psaní sonetů používám pentametr jambický. Rytmus je klíčový pro krásu verše.', 'procedural', shakespeare_personality_id, 0.7,
     '{"technique": "iambic_pentameter", "form": "sonnet", "craft": "poetry"}'),
    ('Láska je jako růže - krásná, ale s trny. Toto téma se prolíná mnoha mými hrami.', 'semantic', shakespeare_personality_id, 0.8,
     '{"theme": "love", "metaphor": "rose", "literary_device": "symbolism"}');
    
    -- Vložení ukázkových dokumentů
    INSERT INTO documents (title, filename, content, document_type, personality_id, metadata, is_processed) VALUES 
    ('Teorie relativity - základy', 'relativity_basics.txt', 
     'Speciální teorie relativity (1905) a obecná teorie relativity (1915) představují dva pilíře moderní fyziky. Speciální teorie se zabývá objekty pohybujícími se konstantní rychlostí, zatímco obecná teorie popisuje gravitaci jako zakřivení časoprostoru.', 
     'text', einstein_personality_id, '{"source": "scientific_paper", "year": "1905-1915"}', true),
    ('Shakespearovy sonety - analýza', 'sonnets_analysis.txt',
     'Shakespearových 154 sonetů představuje vrchol anglické renesanční poezie. Sonety 1-126 jsou věnovány mladému muži, sonety 127-152 temné dámě. Sonet 18 "Shall I compare thee to a summer''s day?" patří k nejznámějším.',
     'text', shakespeare_personality_id, '{"source": "literary_analysis", "period": "renaissance"}', true);
    
    -- Vložení ukázkových konverzací
    INSERT INTO conversations (personality_id, user_id, title, metadata) VALUES 
    (einstein_personality_id, test_user_id, 'Diskuse o kvantové mechanice', '{"topic": "quantum_mechanics", "difficulty": "advanced"}'),
    (shakespeare_personality_id, demo_user_id, 'O lásce a poezii', '{"topic": "love_poetry", "style": "casual"}')
    RETURNING id INTO conversation_id;
    
    -- Vložení ukázkových zpráv do konverzací
    -- Pro Einstein konverzaci
    SELECT id INTO conversation_id FROM conversations WHERE title = 'Diskuse o kvantové mechanice';
    INSERT INTO conversation_messages (conversation_id, role, content, metadata) VALUES 
    (conversation_id, 'user', 'Můžete mi vysvětlit kvantovou mechaniku jednoduše?', '{"timestamp": "2023-12-01T10:00:00Z"}'),
    (conversation_id, 'assistant', 'Kvantová mechanika je fascinující oblast fyziky, která popisuje chování velmi malých částic. Na kvantové úrovni se částice chovají velmi odlišně od našich každodenních zkušeností. Například elektron může být současně na více místech - to nazýváme superpozice. Teprve když ho pozorujeme, "rozhodne" se, kde skutečně je.', '{"timestamp": "2023-12-01T10:01:00Z", "personality_response": true}'),
    (conversation_id, 'user', 'To zní neuvěřitelně! Jak jste na to přišel?', '{"timestamp": "2023-12-01T10:02:00Z"}'),
    (conversation_id, 'assistant', 'Musím přiznat, že kvantová mechanika mě zpočátku také znepokojovala. Slavně jsem řekl "Bůh nehraje kostky", protože jsem věřil v deterministický vesmír. Ale experimenty ukázaly, že na kvantové úrovni skutečně existuje fundamentální náhodnost. Max Planck, Niels Bohr a další vybudovali teorii, kterou jsem zpočátku odmítal, ale nakonec jsem musel uznat její správnost.', '{"timestamp": "2023-12-01T10:03:00Z", "personality_response": true}');
    
    -- Pro Shakespeare konverzaci
    SELECT id INTO conversation_id FROM conversations WHERE title = 'O lásce a poezii';
    INSERT INTO conversation_messages (conversation_id, role, content, metadata) VALUES 
    (conversation_id, 'user', 'Jak byste popsal lásku v moderní době?', '{"timestamp": "2023-12-01T14:00:00Z"}'),
    (conversation_id, 'assistant', 'Ach, láska! Věčné téma, které se nemění ani s časem. I v této moderní době, kdy lidé komunikují prostřednictvím světelných signálů a kovových zařízení, srdce stále bije stejným rytmem. Láska je stále "růže pod jakýmkoli jménem voní stejně sladce". Možná se změnily způsoby dvorby, ale podstata lásky - ta touha po spojení duší, ta sladká agónie oddělení, ta radost ze setkání - to vše zůstává nezměněno.', '{"timestamp": "2023-12-01T14:01:00Z", "personality_response": true}'),
    (conversation_id, 'user', 'Napsal byste dnes sonety o sociálních sítích?', '{"timestamp": "2023-12-01T14:02:00Z"}'),
    (conversation_id, 'assistant', 'Zajisté! Představte si sonet o těchto "sociálních sítích":\n\n"Když obrazovka svítí v temné noci,\nA prsty tančí po klávesách snů,\nHledáme lásku v digitální moci,\nV světě, kde srdce mluví bez slov.\n\nLike za like, a srdce rychle bije,\nEmoji nahrazuje polibek,\nAle duše stále touhou žije\nPo dotyku, ne jen po písmek.\n\nOh moderní lásko, divná jsi a nová,\nPřes kabely a vlny se snažíš létat,\nAle pravá láska, ta je stále prostá -\nOči do očí, srdce k srdci vzlétat."\n\nVidíte? I v digitálním věku láska hledá svou cestu!', '{"timestamp": "2023-12-01T14:03:00Z", "personality_response": true}');
    
END $$;

-- Vložení ukázkových chunk embeddings (simulované hodnoty)
-- V reálném systému by tyto hodnoty generoval embedding model
INSERT INTO document_chunks (document_id, content, chunk_index, metadata) 
SELECT d.id, 
       CASE 
           WHEN d.title = 'Teorie relativity - základy' THEN 'Speciální teorie relativity (1905) se zabývá objekty pohybujícími se konstantní rychlostí.'
           WHEN d.title = 'Shakespearovy sonety - analýza' THEN 'Shakespearových 154 sonetů představuje vrchol anglické renesanční poezie.'
       END,
       0,
       '{"chunk_type": "paragraph", "word_count": 15}'
FROM documents d 
WHERE d.title IN ('Teorie relativity - základy', 'Shakespearovy sonety - analýza');

-- Poznámka: V reálném systému by se embeddings generovaly automaticky pomocí ML modelů
-- Zde vkládáme pouze ukázkové záznamy pro demonstraci struktury

COMMIT;
