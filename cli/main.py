"""
NESTOR CLI - Command Line Interface
Nástroje pro správu NESTOR platformy
"""

import typer
from rich.console import Console
from rich.table import Table
import httpx
import asyncio
from typing import Optional

app = typer.Typer(
    name="nestor",
    help="NESTOR CLI - Nástroje pro správu digitálních osobností",
    add_completion=False
)

console = Console()

# Konfigurace služeb
SERVICES = {
    "api": "http://localhost:8000",
    "memory": "http://localhost:8001", 
    "rag": "http://localhost:8002",
    "llm": "http://localhost:8003",
    "tokenization": "http://localhost:8004"
}

@app.command()
def status():
    """Zobrazí stav všech NESTOR služeb"""
    console.print("[bold blue]NESTOR Services Status[/bold blue]")
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Service", style="cyan")
    table.add_column("URL", style="yellow")
    table.add_column("Status", style="green")
    
    async def check_service(name: str, url: str):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{url}/health", timeout=5.0)
                if response.status_code == 200:
                    return "✅ Healthy"
                else:
                    return f"❌ Error ({response.status_code})"
        except Exception as e:
            return f"❌ Unavailable ({str(e)[:30]}...)"
    
    async def check_all_services():
        for name, url in SERVICES.items():
            status = await check_service(name, url)
            table.add_row(name.upper(), url, status)
    
    asyncio.run(check_all_services())
    console.print(table)

@app.command()
def info():
    """Zobrazí informace o NESTOR platformě"""
    console.print("[bold green]NESTOR Platform Information[/bold green]")
    console.print()
    console.print("🤖 [bold]NESTOR[/bold] - Platforma pro vytváření a tokenizaci digitálních osobností")
    console.print()
    console.print("[bold cyan]Klíčové funkce:[/bold cyan]")
    console.print("• Vytváření digitálních osobností pomocí AI")
    console.print("• Retrieval-Augmented Generation (RAG)")
    console.print("• Tokenizace na blockchainu")
    console.print("• Digitální nesmrtelnost")
    console.print()
    console.print("[bold cyan]Technologie:[/bold cyan]")
    console.print("• FastAPI mikroslužby")
    console.print("• PostgreSQL s pgvector")
    console.print("• Redis cache")
    console.print("• Docker/Podman kontejnery")

@app.command()
def logs(
    service: Optional[str] = typer.Argument(None, help="Název služby (api, memory, rag, llm, tokenization)"),
    follow: bool = typer.Option(False, "--follow", "-f", help="Sledovat logy v reálném čase")
):
    """Zobrazí logy služeb"""
    if service:
        if service not in SERVICES:
            console.print(f"[red]Neznámá služba: {service}[/red]")
            console.print(f"Dostupné služby: {', '.join(SERVICES.keys())}")
            raise typer.Exit(1)
        
        container_name = f"nestor-{service}"
        if service == "rag":
            container_name = "nestor-rag"
        elif service == "memory":
            container_name = "nestor-mcp"
        
        cmd = f"podman logs {container_name}"
        if follow:
            cmd += " -f"
        
        console.print(f"[cyan]Zobrazuji logy pro službu: {service}[/cyan]")
        import subprocess
        subprocess.run(cmd, shell=True)
    else:
        console.print("[yellow]Zadejte název služby nebo použijte --help pro nápovědu[/yellow]")

@app.command()
def version():
    """Zobrazí verzi NESTOR CLI"""
    console.print("[bold green]NESTOR CLI v1.0.0[/bold green]")

if __name__ == "__main__":
    app()
